import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { HospitalExaminationFormListScreen } from '@/features/hospital-partner/screens/HospitalExaminationFormListScreen/HospitalExaminationFormListScreen'
import { useLocalSearchParams, useRouter } from 'expo-router'
import { useTranslation } from 'react-i18next'
export default function HospitalExaminationFormListAppScreen() {
  const { t } = useTranslation()
  const { facultyName } = useLocalSearchParams()

  const router = useRouter()

  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: (facultyName as string) || t('MES-543'),
          headerShown: true,
        }}
      />
      <HospitalExaminationFormListScreen />
    </>
  )
}
