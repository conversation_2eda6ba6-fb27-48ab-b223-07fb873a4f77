import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { CreateHospitalExaminationFormScreen } from '@/features/hospital-partner/screens/CreateHospitalExaminationFormScreen/CreateHospitalExaminationFormScreen'
import { useRouter } from 'expo-router'
import { useTranslation } from 'react-i18next'
export default function CreateHospitalExaminationFormAppScreen() {
  const { t } = useTranslation()

  const router = useRouter()

  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-470'),
          headerShown: true,
        }}
      />
      <CreateHospitalExaminationFormScreen />
    </>
  )
}
