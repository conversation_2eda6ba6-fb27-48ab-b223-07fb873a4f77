import { BaseWebView } from '@/components/BaseWebView/BaseWebView'
import { StackScreenBase } from '@/components/StackBase/StackScreenBase'
import { MESSAGE_NATIVE } from '@/constants/message-native.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { useSaveImageToGallery } from '@/hooks/common/useSaveImageToGallery'
import { useSavePDFBase64 } from '@/hooks/common/useSavePDFBase64'

import { WEBVIEW_APP_ROUTES } from '@/routes/webviewRoutes'
import { useLocalSearchParams, useRouter } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import Toast from 'react-native-toast-message'
import { WebViewMessageEvent } from 'react-native-webview'

export default function HospitalPartnersExaminationFormScreen() {
  const { t } = useTranslation()
  const { user } = useAuthentication()
  const router = useRouter()
  const { savePDF } = useSavePDFBase64()
  const { saveImage } = useSaveImageToGallery()
  const { id } = useLocalSearchParams()
  const handleMessage = async (e: WebViewMessageEvent) => {
    const event = JSON.parse(e.nativeEvent.data)
    switch (event.action) {
      case MESSAGE_NATIVE.saveExaminationPDF: {
        const { pdf, filename } = event?.payload || {}
        await savePDF({ pdf, filename, options: {} })
        break
      }
      case MESSAGE_NATIVE.saveExaminationImage: {
        const { image, filename } = event?.payload || {}
        await saveImage({
          image,
          filename,
          options: {},
          onSuccess: () => {
            Toast.show({
              type: 'success',
              text1: t('MES-662'),
            })
          },
          onError: () => {
            Toast.show({
              type: 'error',
              text1: t('MES-663'),
            })
          },
        })
        break
      }
      case MESSAGE_NATIVE.closeExaminationSuccessPopup: {
        // Multiple back to Examination Form List Screen
        router.back()
        router.back()
      }
      default:
        break
    }
  }
  const insets = useSafeAreaInsets()
  return (
    <>
      <StackScreenBase
        options={{
          headerTitle: t('MES-533'),
        }}
      />
      <BaseWebView
        source={{ uri: WEBVIEW_APP_ROUTES.EXAMINATION?.path + '/form/' + id }}
        onMessage={handleMessage}
        style={{
          paddingBottom: insets.bottom,
        }}
      />
    </>
  )
}
