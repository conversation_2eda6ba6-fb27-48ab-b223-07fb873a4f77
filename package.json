{"name": "wap-native", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "format": "prettier --write ."}, "dependencies": {"@expo/config-plugins": "~10.1.1", "@chatwoot/react-native-widget": "^0.0.21", "@expo/html-elements": "^0.12.5", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.53", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/toast": "^1.0.9", "@gorhom/bottom-sheet": "^5.1.6", "@hookform/resolvers": "^5.1.1", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/messaging": "^22.4.0", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@rn-primitives/slot": "^1.2.0", "@rn-primitives/tabs": "^1.2.0", "@rn-primitives/types": "^1.2.0", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "expo": "53.0.20", "expo-apple-authentication": "~7.2.4", "expo-audio": "~0.4.8", "expo-auth-session": "~6.2.1", "expo-av": "~15.1.7", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-constants": "~17.1.7", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-file-system": "~18.1.11", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.5", "expo-localization": "~16.1.6", "expo-media-library": "~17.1.7", "expo-notifications": "~0.31.2", "expo-router": "~5.1.4", "expo-secure-store": "^14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.17", "expo-web-browser": "~14.2.0", "i18next": "^25.3.0", "immer": "^10.1.1", "lodash-es": "^4.17.21", "nativewind": "^4.1.23", "qs-esm": "^7.0.2", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.5.3", "react-native": "0.79.5", "react-native-awesome-gallery": "^0.4.3", "react-native-css-interop": "^0.1.22", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-shadow-2": "^7.1.2", "react-native-svg": "15.11.2", "react-native-toast-message": "2.3.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/lodash-es": "^4.17.12", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "jscodeshift": "^0.15.2", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.5.14", "react-native-svg-transformer": "^1.5.1", "tailwindcss": "^3.4.17", "tsx": "^4.20.4", "typescript": "~5.8.3"}, "private": true}