import ArrowRightIcon from '@/assets/icons/arrow-right-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useGetRandomKeywords } from '@/hooks/query/keyword/useGetRandomKeywords'
import { Keyword } from '@/types/keyword.type'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { KeywordItem } from '../KeywordItem/KeywordItem'
interface RelatedKeywordsProps {
  categories: Keyword['categories'] | null
  isRefreshing?: boolean
  id: string
}
export const RelatedKeywords = ({ categories, isRefreshing, id }: RelatedKeywordsProps) => {
  const { t } = useTranslation()
  const { randomKeywords, isGetRandomKeywordsLoading, isFetching, refetch } = useGetRandomKeywords({
    params: {
      locale: 'all',
      limit: 5,
      categories: {
        in: categories ? categories : undefined,
      },
      select: {
        name: true,
        id: true,
        hiragana: true,
      },
    },
    useQueryOptions: {
      staleTime: 3 * 60 * 1000,
    },
  })

  const filteredCurrentKeyword = randomKeywords?.docs?.filter((keyword) => keyword.id !== id)
  useEffect(() => {
    if (isRefreshing) {
      refetch()
    }
  }, [isRefreshing])
  if (!filteredCurrentKeyword?.length && !isGetRandomKeywordsLoading) return null
  return (
    <View className="flex flex-col gap-y-4 px-4">
      <Text size="body3" variant="default">
        {t('MES-574')}
      </Text>
      {isGetRandomKeywordsLoading || isFetching || isRefreshing ? (
        <RelatedKeywordsLoading />
      ) : (
        <View className="flex flex-col gap-y-2">
          {filteredCurrentKeyword?.map((keyword, index) => (
            <KeywordItem
              key={`${keyword.id}-${index}`}
              keyword={keyword as Keyword}
              isShowArchiveButton={false}
              navigationMode="push"
              renderCustomRightCard={
                <View className="flex-row items-center gap-x-2 self-center ">
                  <ArrowRightIcon width={20} height={20} />
                </View>
              }
            />
          ))}
        </View>
      )}
    </View>
  )
}

const RelatedKeywordsLoading = () => {
  return (
    <View className="flex flex-col gap-y-4 ">
      {new Array(4).fill(0).map((_, index) => (
        <Skeleton key={index} className="h-16 w-full" />
      ))}
    </View>
  )
}
