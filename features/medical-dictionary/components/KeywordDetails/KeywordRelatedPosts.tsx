import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useGetPosts } from '@/hooks/query/post/useGetPosts'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import { useQueryClient } from '@tanstack/react-query'
import { Link, LinkProps } from 'expo-router'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { ScrollView, TouchableOpacity, View } from 'react-native'
interface KeywordRelatedPostsProps {
  id: string
  isRefreshing?: boolean
}
export const KeywordRelatedPosts = ({ id, isRefreshing }: KeywordRelatedPostsProps) => {
  const queryClient = useQueryClient()
  const { t } = useTranslation()
  const { primaryLanguage } = useAppLanguage()
  const { posts, isGetPostsLoading, refetch, isFetching } = useGetPosts({
    params: {
      //   language: primaryLanguage,
      limit: 5,
      where: {
        relatedKeywords: {
          in: id ? id : undefined,
        },
        language: { equals: primaryLanguage as LocaleEnum },
      },
      select: {
        title: true,
        id: true,
        heroImage: true,
        slug: true,
      },
    },
    useQueryOptions: {
      staleTime: 3 * 60 * 1000,
    },
  })
  useEffect(() => {
    if (isRefreshing) {
      refetch()
    }
  }, [isRefreshing])
  if (!posts?.docs.length && !isGetPostsLoading) return null
  return (
    <View className="flex flex-col gap-y-4 px-4">
      <Text size="body3" variant="default">
        {t('MES-771')}
      </Text>
      {isGetPostsLoading || isFetching || isRefreshing ? (
        <KeywordRelatedPostsLoading />
      ) : (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: 12 }}
          className="bg-white "
          pagingEnabled
          snapToInterval={156 + 12}
          decelerationRate={'fast'}
        >
          {posts?.docs.map((post, index) => {
            const { heroImage, title, slug } = post || {}
            const { url, thumbnailURL } = heroImage as Media
            const imageUrl = thumbnailURL || url || ''

            return (
              <Link
                key={post.id || index}
                href={
                  {
                    pathname: APP_ROUTES.POSTS.path + '/[slug]',
                    params: {
                      slug: slug,
                    },
                  } as LinkProps['href']
                }
                asChild
              >
                <TouchableOpacity
                  className="flex flex-col gap-y-2 overflow-hidden p-2"
                  style={{
                    width: 256,
                  }}
                >
                  <View className="aspect-video overflow-hidden rounded-lg">
                    <StyledExpoImage
                      source={{
                        uri: imageUrl,
                      }}
                      contentFit="cover"
                      className="h-full w-full"
                      placeholder={BLURHASH_CODE}
                    />
                  </View>
                  <Text size="body6" variant="default" numberOfLines={2}>
                    {title}
                  </Text>
                </TouchableOpacity>
              </Link>
            )
          })}
        </ScrollView>
      )}
    </View>
  )
}

const KeywordRelatedPostsLoading = () => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{ gap: 12 }}
      className="bg-white "
      pagingEnabled
      snapToInterval={156 + 12}
      decelerationRate={'fast'}
    >
      {new Array(4).fill(0).map((_, index) => (
        <View
          key={index}
          className="aspect-video overflow-hidden rounded-lg"
          style={{
            width: 240,
          }}
        >
          <Skeleton className="h-full w-full" />
        </View>
      ))}
    </ScrollView>
  )
}
