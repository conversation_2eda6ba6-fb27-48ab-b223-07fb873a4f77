import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { BLURHASH_CODE } from '@/constants/global.constant'
import { useGetFilteredProducts } from '@/features/product/hooks/query/product/useGetFilteredProducts'
import { StyledExpoImage } from '@/libs/styled'
import { APP_ROUTES } from '@/routes/appRoutes'
import { Media } from '@/types/media.type'
import { Link, LinkProps } from 'expo-router'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { ScrollView, TouchableOpacity, View } from 'react-native'
interface KeywordRelatedProductsProps {
  id: string
  isRefreshing?: boolean
}
export const KeywordRelatedProducts = ({ id, isRefreshing }: KeywordRelatedProductsProps) => {
  const { t } = useTranslation()

  const { filteredProducts, isGetFilteredProductsLoading, isFetching, refetch } =
    useGetFilteredProducts({
      params: {
        //   language: primaryLanguage,
        limit: 5,
        where: {
          keywords: {
            in: id ? id : undefined,
          },
        },
        select: {
          title: true,
          id: true,
          heroImage: true,
          slug: true,
        },
      },
      useQueryOptions: {
        staleTime: 3 * 60 * 1000,
      },
    })
  useEffect(() => {
    if (isRefreshing) {
      refetch()
    }
  }, [isRefreshing])

  if (!filteredProducts?.docs.length && !isGetFilteredProductsLoading) return null
  return (
    <View className="flex flex-col gap-y-4 px-4">
      <Text size="body3" variant="default">
        {t('MES-772')}
      </Text>
      {isGetFilteredProductsLoading || isFetching || isRefreshing ? (
        <KeywordRelatedProductsLoading />
      ) : (
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: 12 }}
          className="bg-white "
          pagingEnabled
          snapToInterval={156 + 12}
          decelerationRate={'fast'}
        >
          {filteredProducts?.docs.map((product, index) => {
            const { heroImage, title, slug } = product || {}
            const { url, thumbnailURL } = heroImage as Media
            const imageUrl = thumbnailURL || url || ''

            return (
              <Link
                key={product.id}
                href={
                  (APP_ROUTES.PRODUCTS.children?.PRODUCTS_DETAIL_V2.path +
                    `/${product.slug}`) as LinkProps['href']
                }
                asChild
              >
                <TouchableOpacity
                  key={product.id || index}
                  className="flex flex-col gap-y-2 overflow-hidden p-2"
                  style={{
                    width: 256,
                  }}
                >
                  <View className="aspect-video overflow-hidden rounded-lg">
                    <StyledExpoImage
                      source={{
                        uri: imageUrl,
                      }}
                      contentFit="cover"
                      className="h-full w-full"
                      placeholder={BLURHASH_CODE}
                    />
                  </View>
                  <Text size="body6" variant="default" numberOfLines={2}>
                    {title}
                  </Text>
                </TouchableOpacity>
              </Link>
            )
          })}
        </ScrollView>
      )}
    </View>
  )
}

const KeywordRelatedProductsLoading = () => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={{ gap: 12 }}
      className="bg-white "
      pagingEnabled
      snapToInterval={156 + 12}
      decelerationRate={'fast'}
    >
      {new Array(4).fill(0).map((_, index) => (
        <View
          key={index}
          className="aspect-video overflow-hidden rounded-lg"
          style={{
            width: 240,
          }}
        >
          <Skeleton className="h-full w-full" />
        </View>
      ))}
    </ScrollView>
  )
}
