import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { useGetKeywordDetails } from '@/hooks/query/keyword/useGetKeywordDetails'
import colors from '@/styles/_colors'
import { Keyword, KeywordV2 } from '@/types/keyword.type'
import { useLocalSearchParams } from 'expo-router'
import { useEffect, useState } from 'react'
import { RefreshControl, ScrollView, View } from 'react-native'
import { KeywordDetailsContent } from './KeywordDetailsContent'
import { KeywordDetailsHeader } from './KeywordDetailsHeader'
import { KeywordRelatedPosts } from './KeywordRelatedPosts'
import { KeywordRelatedProducts } from './KeywordRelatedProducts'
import { RelatedFaculties } from './RelatedFaculties'
import { RelatedKeywords } from './RelatedKeywords'

export const KeywordDetails = () => {
  const { id } = useLocalSearchParams()

  const [refreshing, setRefreshing] = useState(false)
  const [shouldRefreshChildren, setShouldRefreshChildren] = useState(false)
  const {
    keywordDetails,
    isGetKeywordDetailsError,
    isGetKeywordDetailsLoading,
    isFetching,
    refetch,
  } = useGetKeywordDetails({
    id: id as string,
    params: {
      locale: 'all',
      convertDescriptionHTML: true,
      withFavoriteFlag: true,
    },
    useQueryOptions: {
      staleTime: 0,
      gcTime: 0,
      enabled: Boolean(id),
    },
  })
  const { isFavorite } = (keywordDetails as KeywordV2) || {}

  // Reset shouldRefreshChildren after child components have had a chance to refresh
  useEffect(() => {
    if (shouldRefreshChildren) {
      const timer = setTimeout(() => {
        setShouldRefreshChildren(false)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [shouldRefreshChildren])

  const handleRefresh = async () => {
    setRefreshing(true)
    setShouldRefreshChildren(false)

    try {
      await refetch()
      // Only trigger child refreshes after main fetch is done
      setShouldRefreshChildren(true)
    } finally {
      setRefreshing(false)
    }
  }

  if (!id || isGetKeywordDetailsError) return null

  return (
    <View className="flex-1 bg-white">
      <KeywordDetailsHeader
        isFavorite={isFavorite}
        id={id as string}
        isLoading={isGetKeywordDetailsLoading || isFetching}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[colors.primary['500']]}
            tintColor={colors.primary['500']}
            progressBackgroundColor="#FFFFFF"
            progressViewOffset={0}
          />
        }
      >
        {isGetKeywordDetailsLoading || isFetching ? (
          <KeywordDetailsLoading />
        ) : (
          <View className=" flex flex-col gap-y-4 py-3">
            <KeywordDetailsContent keyword={keywordDetails as unknown as Keyword} />
            <RelatedKeywords
              categories={keywordDetails?.categories}
              isRefreshing={shouldRefreshChildren}
              id={id as string}
            />
            <RelatedFaculties id={id as string} isRefreshing={shouldRefreshChildren} />
            <KeywordRelatedPosts id={id as string} isRefreshing={shouldRefreshChildren} />
            <KeywordRelatedProducts id={id as string} isRefreshing={shouldRefreshChildren} />
          </View>
        )}
      </ScrollView>
    </View>
  )
}

const KeywordDetailsLoading = () => {
  return (
    <View
      className=" flex flex-1 flex-col bg-white px-4 py-3 "
      style={{
        gap: 24,
      }}
    >
      <Skeleton className=" h-[120px] w-full rounded-lg" />
      <View className="flex-col gap-y-3">
        <View className="h-10  rounded-lg" style={{ width: '40%' }}>
          <Skeleton className="h-full w-full" />
        </View>
        <View className="flex-col gap-y-2">
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
          <Skeleton className="h-8 w-full" />
        </View>
      </View>

      <View className="w-full flex-col gap-y-4">
        <View className="h-10  rounded-lg" style={{ width: '40%' }}>
          <Skeleton className="h-full w-full" />
        </View>
        <View className="w-full flex-col gap-y-3">
          {new Array(3).fill(0).map((_, index) => (
            <View key={index} className="flex-1 flex-row gap-x-3">
              <View
                style={{
                  width: 64,
                  height: 64,
                  borderRadius: '100%',
                }}
              >
                <Skeleton className="h-full w-full rounded-full" />
              </View>
              <View className="w-full flex-1 flex-col gap-y-2">
                <Skeleton className="h-8 w-1/3" />
                <Skeleton className="h-12 w-full" />
              </View>
            </View>
          ))}
        </View>
      </View>

      <View className="flex-col gap-y-4">
        <View className="h-10  rounded-lg" style={{ width: '40%' }}>
          <Skeleton className="h-full w-full" />
        </View>
        <View className="flex-col gap-y-3">
          {new Array(3).fill(0).map((_, index) => (
            <Skeleton key={index} className="h-12 w-full" />
          ))}
        </View>
      </View>
    </View>
  )
}
