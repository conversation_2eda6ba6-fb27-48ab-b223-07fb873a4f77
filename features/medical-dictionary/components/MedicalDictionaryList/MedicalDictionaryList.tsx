import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import colors from '@/styles/_colors'
import { KeywordV2 } from '@/types/keyword.type'
import React, { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, View } from 'react-native'
import { ListItem } from '../../hooks/common/useMedicalDictionaryList'
import { KeywordItem } from '../KeywordItem/KeywordItem'

interface MedicalDictionaryListProps {
  data: ListItem[]
  showArchiveButton?: boolean
  refreshing: boolean
  isGetKeywordsError: boolean
  onRefresh: () => void
  onLoadMore: () => void
  headerComponent?: React.ComponentType<any> | React.ReactElement | null
  containerStyle?: string
  onUpdateFavoriteKeywordSuccess?: () => void
  initialLoadingItem?: number
  onPressKeyword?: (keyword: KeywordV2) => void
  customParams?: Record<string, string>
}

export function MedicalDictionaryList({
  data,
  showArchiveButton = false,
  refreshing,
  isGetKeywordsError,
  onRefresh,
  onLoadMore,
  headerComponent,
  containerStyle = 'flex-1 bg-white px-4 pb-2',
  onUpdateFavoriteKeywordSuccess,
  initialLoadingItem = 8,
  onPressKeyword,
  customParams,
}: MedicalDictionaryListProps) {
  const { t } = useTranslation()

  const renderItem = useCallback(
    ({ item }: { item: ListItem }) => {
      if (item.type === 'keyword') {
        return (
          <KeywordItem
            keyword={item.item as KeywordV2}
            isShowArchiveButton={showArchiveButton}
            onUpdateFavoriteKeywordSuccess={onUpdateFavoriteKeywordSuccess}
            onPress={onPressKeyword}
            customParams={customParams}
          />
        )
      }

      if (item.type === 'loading') {
        return (
          <View className="items-center py-4">
            <ActivityIndicator size="small" />
          </View>
        )
      }

      if (item.type === 'loading_skeleton') {
        return (
          <View className="flex flex-col gap-y-2">
            {[...Array.from({ length: initialLoadingItem }).fill(1)].map((_, index) => (
              <Skeleton key={index} className=" h-[72px] rounded-lg bg-gray-200" />
            ))}
          </View>
        )
      }

      return null
    },
    [showArchiveButton, onUpdateFavoriteKeywordSuccess, initialLoadingItem],
  )

  const keyExtractor = useCallback((item: ListItem, index: number) => {
    if (item.type === 'keyword') {
      return `keyword-${item.item.id}`
    }
    if (item.type === 'loading') {
      return `loading-${index}`
    }
    return `item-${index}`
  }, [])

  const renderSeparator = useCallback(() => {
    return <View className="h-2" />
  }, [])

  const renderEmptyComponent = useCallback(() => {
    if (isGetKeywordsError) {
      return (
        <View className="items-center  py-8">
          <Text size="body6" className="text-custom-danger-600">
            {t('MES-614')}
          </Text>
        </View>
      )
    }

    return (
      <View className="flex flex-col items-center justify-center gap-y-2 py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-768')}
        </Text>
      </View>
    )
  }, [isGetKeywordsError, t])

  return (
    <View className={containerStyle}>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        extraData={data}
        ListHeaderComponent={headerComponent}
        ListEmptyComponent={renderEmptyComponent}
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 16 }}
        ItemSeparatorComponent={renderSeparator}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary['500']]}
            tintColor={colors.primary['500']}
            progressBackgroundColor="#FFFFFF"
            // Add these properties to improve refresh behavior
            progressViewOffset={0}
          />
        }
      />
    </View>
  )
}
