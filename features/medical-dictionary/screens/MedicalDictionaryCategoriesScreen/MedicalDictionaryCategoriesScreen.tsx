import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { keywordQueryKeys } from '@/hooks/query/keyword/queryKeys'
import { useLocalSearchParams } from 'expo-router'
import React, { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { KeywordCategoriesSection } from '../../components/KeywordCategoriesSection/KeywordCategoriesSection'
import { MedicalDictionaryList } from '../../components/MedicalDictionaryList/MedicalDictionaryList'
import { useMedicalDictionaryList } from '../../hooks/common/useMedicalDictionaryList'

export default function MedicalDictionaryCategoriesScreen() {
  const { category } = useLocalSearchParams()
  const [activeCategory, setActiveCategory] = useState(category as string)
  const { t } = useTranslation()
  const { user } = useAuthentication()
  const { refreshing, data, isGetKeywordsError, handleRefresh, handleLoadMore, refetch } =
    useMedicalDictionaryList({
      activeCategory: [activeCategory],
      extendedKeys: [activeCategory, ...keywordQueryKeys['dictionary-keywords-by-category'].base()],
    })

  const handlePressCategory = useCallback((category: string) => {
    setActiveCategory(category)
  }, [])
  // useEffect(() => {
  //   return () => {
  //     queryClient.resetQueries({
  //       queryKey: ['dictionary-keywords-by-category'],
  //     })
  //   }
  // }, [])
  const renderHeaderComponent = useCallback(() => {
    return (
      <View className="gap-y-4">
        <KeywordCategoriesSection
          activeCategory={activeCategory}
          onPressCategory={handlePressCategory}
          isShowTitle={false}
          mode="collapse"
          enableRepositioning={true}
          defaultItemsToShow={4}
        />

        {/* List Header */}
        <View className="mb-3">
          <Text size="body3" className="font-medium">
            {t('MES-565')}
          </Text>
        </View>
      </View>
    )
  }, [activeCategory, handlePressCategory, t, refreshing])

  return (
    <MedicalDictionaryList
      data={data}
      refreshing={refreshing}
      isGetKeywordsError={isGetKeywordsError}
      onRefresh={handleRefresh}
      onLoadMore={handleLoadMore}
      headerComponent={renderHeaderComponent()}
      containerStyle="flex-1 bg-white p-4"
      showArchiveButton={Boolean(user)}
      onUpdateFavoriteKeywordSuccess={() => {
        refetch()
      }}
    />
  )
}
