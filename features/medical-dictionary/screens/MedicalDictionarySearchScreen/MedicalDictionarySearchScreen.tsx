import { useEffect } from 'react'

import { KeyboardAvoidingView, Platform, SafeAreaView } from 'react-native'

import { useShallow } from 'zustand/react/shallow'
import { SearchKeywords } from '../../components/SearchKeywords/SearchKeywords'
import { useMedicalDictionaryStore } from '../../stores/MedicalDictionaryStore'

export const MedicalDictionarySearchScreen = () => {
  const { clearAllFiltersAndSearchText, searchTextValue } = useMedicalDictionaryStore(
    useShallow((state) => ({
      clearAllFiltersAndSearchText: state.clearAllFiltersAndSearchText,
      searchTextValue: state.searchText,
    })),
  )

  useEffect(() => {
    return () => {
      clearAllFiltersAndSearchText()
    }
  }, [])

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
        enabled={true}
      >
        <SearchKeywords />
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}
