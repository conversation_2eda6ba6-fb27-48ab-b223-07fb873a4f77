import { API_ENDPOINTS } from '@/constants/endpoint.constant'

import { httpService } from '@/services/http/http.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'
import { KeywordSearchHistory } from '../types'

// SERVER / CLIENT
class KeywordSearchHistoryService {
  private static instance: KeywordSearchHistoryService

  private constructor() {}

  public static getInstance(): KeywordSearchHistoryService {
    if (!KeywordSearchHistoryService.instance) {
      KeywordSearchHistoryService.instance = new KeywordSearchHistoryService()
    }
    return KeywordSearchHistoryService.instance
  }

  async getKeywordSearchHistoryByUser({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<KeywordSearchHistory>> {
    const data = await httpService.get<PaginatedDocs<KeywordSearchHistory>>(
      `/${API_ENDPOINTS.keyword_search_history_api}/user`,
      {
        params,
        ...options,
      },
    )
    return data
  }

  async updateKeywordSearchHistory(id: string, options?: AxiosRequestConfig): Promise<void> {
    await httpService.post(
      `/${API_ENDPOINTS.keyword_search_history_api}/update-history/${id}`,
      undefined,
      options,
    )
  }

  async deleteKeywordSearchHistory(id: string, options?: AxiosRequestConfig) {
    await httpService.delete(`/${API_ENDPOINTS.keyword_search_history_api}/delete/${id}`, options)
  }

  async deleteAllKeywordSearchHistory(options?: AxiosRequestConfig) {
    await httpService.delete(`/${API_ENDPOINTS.keyword_search_history_api}/delete-all`, options)
  }
}

export const keywordSearchHistoryService = KeywordSearchHistoryService.getInstance()
