import GlobalIcon from '@/assets/icons/global-icon.svg'
import HeartIcon from '@/assets/icons/heart-icon.svg'
import HiraganaIcon from '@/assets/icons/hiragana-icon.svg'
import LockIcon from '@/assets/icons/lock-icon.svg'
import LogoutIcon from '@/assets/icons/logout-icon.svg'
import ProfileIcon from '@/assets/icons/profile-icon.svg'
import VietnamFlag from '@/assets/icons/vietnam-flag.svg'
import JapanFlag from '@/assets/login/japan-flag.svg'
import { useOpenDeactivateAccountPopup } from '@/components/Popup/DeactivateAccountPopup/DeactivateAccountPopup'
import { useOpenLogoutPopup } from '@/components/Popup/LogoutPopup/LogoutPopup'
import { Text, VariantKey } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAuthentication } from '@/hooks/auth/useAuthentication'

import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useLoadingScreen } from '@/hooks/common/useLoadingScreen'
import { APP_ROUTES } from '@/routes/appRoutes'
import { useUIConfigStore } from '@/stores/UIConfigStore/UIConfigStore'
import { neutral, primary } from '@/styles/_colors'
import { Link, LinkProps } from 'expo-router'
import React, { ReactNode, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Switch, TouchableOpacity, View } from 'react-native'
import Toast from 'react-native-toast-message'
export const ProfileMenu = () => {
  const { logout, deactivateAccount } = useAuthentication()

  const { t } = useTranslation()
  const { showFurigana, setShowFurigana } = useUIConfigStore()
  const { primaryLanguage } = useAppLanguage()
  const { showLoading, hideLoading } = useLoadingScreen()
  const { openDeactivateAccountPopup } = useOpenDeactivateAccountPopup()
  const MENU_LIST: ProfileMenuItemProps[] = useMemo(() => {
    return [
      {
        name: t('MES-59'),
        icon: <ProfileIcon />,
        url: APP_ROUTES.EDIT_PROFILE.path,
      },
      {
        name: t('MES-42'),
        icon: <GlobalIcon />,
        rightElement: primaryLanguage === LocaleEnum.VI ? <VietnamFlag /> : <JapanFlag />,
        url: APP_ROUTES.SWITCH_LANGUAGE.path,
      },

      {
        name: t('MES-152'),
        icon: <HiraganaIcon />,
        rightElement: (
          <View
            style={{ transform: [{ scaleX: 0.7 }, { scaleY: 0.7 }] }}
            className="flex justify-end self-start"
          >
            <Switch
              value={showFurigana}
              onValueChange={setShowFurigana}
              trackColor={{ false: neutral[200], true: primary[500] }}
              thumbColor={neutral.white}
            />
          </View>
        ),
      },
      {
        name: t('MES-183'),
        icon: <LockIcon />,
        url: APP_ROUTES.CHANGE_PASSWORD.path,
      },
    ]
  }, [showFurigana, setShowFurigana, t, primaryLanguage])
  const { openLogoutPopup } = useOpenLogoutPopup()
  const handleLogout = async (close?: () => void) => {
    try {
      showLoading()
      await logout()
      Toast.show({
        type: 'success',
        text1: t('MES-194'),
        text2: t('MES-195'),
        autoHide: true,
      })
      // setTimeout(() => {
      //   router.replace({
      //     pathname: APP_ROUTES.HOME.path,
      //     params: {
      //       overrideRedirect: APP_ROUTES.HOME.path,
      //     },
      //   } as LinkProps['href'])

      // }, 300)
    } catch (error) {
      console.error('Logout error:', error)
      // Show error toast
      Toast.show({
        type: 'error',
        text1: t('Error'),
        text2: t('Logout failed'),
      })
    } finally {
      close?.()
      hideLoading()
    }
  }

  const handleDeactivateAccount = async (close?: () => void) => {
    try {
      showLoading()
      const result = await deactivateAccount()
      if (result.success) {
        await logout()
        Toast.show({
          type: 'success',
          text1: t('MES-701'),
        })

        // setTimeout(() => {
        //   router.replace({
        //     pathname: APP_ROUTES.HOME.path,
        //     params: {
        //       overrideRedirect: APP_ROUTES.HOME.path,
        //     },
        //   } as LinkProps['href'])
        // }, 300)
      }
    } catch (error) {
      console.error('Deactivate account error:', error)
    } finally {
      close?.()
      hideLoading()
    }
  }

  const MENU_LIST_SECONDARY: ProfileMenuItemProps[] = useMemo(
    () => [
      {
        name: t('MES-54'),
        icon: <HeartIcon />,
        url: APP_ROUTES.FAVORITE_PRODUCT.path,
      },

      {
        name: t('MES-57'),
        icon: <LogoutIcon />,
        variant: 'error',
        action: () => {
          openLogoutPopup(handleLogout)
        },
      },
      // {
      //   name: t('MES-702'),
      //   icon: <DeactivateAccountIcon />,
      //   variant: 'error',
      //   action: () => {
      //     openDeactivateAccountPopup(handleDeactivateAccount)
      //   },
      // },
    ],
    [t],
  )

  return (
    <View className="flex flex-col gap-3">
      <View className="rounded-xl bg-custom-background-hover py-2 ">
        {MENU_LIST.map((item) => (
          <MenuItem key={item.name} {...item} />
        ))}
      </View>
      <View className="rounded-xl bg-custom-background-hover py-2 ">
        {MENU_LIST_SECONDARY.map((item) => (
          <MenuItem key={item.name} {...item} />
        ))}
      </View>
    </View>
  )
}
interface ProfileMenuItemProps {
  name: string
  url?: string
  icon: ReactNode
  action?: () => void
  rightElement?: ReactNode
  variant?: VariantKey
}

const MenuItem = ({
  name,
  url,
  icon,
  action,
  rightElement,
  variant = 'default',
}: ProfileMenuItemProps) => {
  return url ? (
    <Link href={url as LinkProps['href']} asChild>
      <TouchableOpacity className="flex-row items-center justify-between gap-3 p-4">
        {icon}
        <Text className="flex-1" variant={variant} size="body6">
          {name}
        </Text>
        {rightElement}
      </TouchableOpacity>
    </Link>
  ) : (
    <TouchableOpacity
      onPress={() => action?.()}
      className="flex-row items-center justify-between gap-3 p-4"
    >
      {icon}
      <Text className="flex-1" variant={variant} size="body6">
        {name}
      </Text>
      {rightElement}
    </TouchableOpacity>
  )
}
