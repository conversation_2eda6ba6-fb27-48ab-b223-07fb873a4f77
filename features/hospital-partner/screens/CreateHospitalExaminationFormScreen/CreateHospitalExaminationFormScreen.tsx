import { KeyboardAvoidingView, Platform } from 'react-native'

import { SafeAreaView } from 'react-native-safe-area-context'
import { CreateHospitalExaminationForm } from '../../components/CreateHospitalExaminationForm/CreateHospitalExaminationForm'
export const CreateHospitalExaminationFormScreen = () => {
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right', 'bottom']}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        className="flex-1"
        enabled={Platform.OS === 'ios'}
      >
        <CreateHospitalExaminationForm />
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}
