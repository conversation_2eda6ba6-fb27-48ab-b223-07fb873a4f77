import { BaseFilterBadgeList } from '@/components/Filter/BaseFilterBadgeList/BaseFilterBadgeList'
import { BaseFilterFooterActions } from '@/components/Filter/BaseFilterFooterActions/BaseFilterFooterActions'
import { BottomSheetFooter, BottomSheetFooterProps } from '@gorhom/bottom-sheet'
import * as Haptics from 'expo-haptics'
import { forwardRef, memo, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { useHospitalStore } from '../../stores'

interface FilterKeywordFooterProps extends BottomSheetFooterProps {
  closeSheet?: () => void
}

export const FilterKeywordFooter = memo(
  forwardRef<View, FilterKeywordFooterProps>(({ closeSheet, ...props }, ref) => {
    return (
      <BottomSheetFooter {...props}>
        <Filter ref={ref} closeSheet={closeSheet} {...props} />
      </BottomSheetFooter>
    )
  }),
)

interface FilterKeywordFooterProps extends BottomSheetFooterProps {
  closeSheet?: () => void
  onLayout?: () => void
}

const Filter = forwardRef<View, FilterKeywordFooterProps>(({ closeSheet, onLayout }, ref) => {
  const { t } = useTranslation()
  const {
    tempFilters,
    applyTempSearchFilters,
    clearAllTempSearchFilters,
    toggleTempSearchHospitalFilter,
  } = useHospitalStore(
    useShallow((state) => ({
      tempFilters: state.tempSearchFilters,
      applyTempSearchFilters: state.applyTempSearchFilters,
      clearAllTempSearchFilters: state.clearAllTempSearchFilters,
      toggleTempSearchHospitalFilter: state.toggleTempSearchHospitalFilter,
    })),
  )

  const handleClearFilter = (filterItem: any) => {
    if (filterItem?.type) {
      toggleTempSearchHospitalFilter(filterItem.type, filterItem)
    }
  }

  const handleApplyFilters = () => {
    applyTempSearchFilters()
    closeSheet?.()
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
  }

  const handleClearAllTempFilters = () => {
    clearAllTempSearchFilters()
  }

  const activeFilters = useMemo(() => {
    return Object.values(tempFilters).filter(Boolean).flat()
  }, [tempFilters])

  return (
    <View
      ref={ref}
      className="relative flex flex-col gap-y-4 border-t border-gray-200 bg-white px-4 py-4 pb-10"
      onLayout={onLayout}
    >
      {/* Selected Filter Badges using ProductFilterBadgeList */}
      <BaseFilterBadgeList
        activeFilters={activeFilters ?? []}
        onClearFilter={handleClearFilter}
        maxDisplayCount={5}
      />

      {/* Footer Actions using BaseFilterKeywordFooterActions */}
      <BaseFilterFooterActions
        onApply={handleApplyFilters}
        onReset={handleClearAllTempFilters}
        applyText={t('MES-281')}
        resetText={t('MES-105')}
      />
    </View>
  )
})
