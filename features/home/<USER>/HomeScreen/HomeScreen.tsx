import React from 'react'
import { KeyboardAvoidingView, Platform, ScrollView, View } from 'react-native'
import { DailyVocabulary } from '../../components/DailyVocabulary/DailyVocabulary'
import { FeatureList } from '../../components/FeatureList/FeatureList'
import { HomeFaculties } from '../../components/HomeFaculties/HomeFaculties'
import { HomePosts } from '../../components/HomePosts/HomePosts'
import { HomeProducts } from '../../components/HomeProducts/HomeProducts'
import { HomeSearch } from '../../components/HomeSearch/HomeSearch'

import { HomeKeywordSearchHistory } from '../../components/HomeKeywordSearchHistory/HomeKeywordSearchHistory'
import { PartnerHospitalsSection } from '../../components/PartnerHospitalsSection/PartnerHospitalsSection'
import { TranslatorContact } from '../../components/TranslatorContact/TranslatorContact'

export default function HomeScreen() {
  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="flex-1"
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        automaticallyAdjustKeyboardInsets
        keyboardShouldPersistTaps="handled"
        contentInsetAdjustmentBehavior="automatic"
        className="flex-1 bg-white"
      >
        <View className="flex-1 flex-col gap-5 pb-6">
          <HomeSearch />
          <PartnerHospitalsSection />
          <HomeKeywordSearchHistory />
          <TranslatorContact />
          <DailyVocabulary />
          <FeatureList />
          {/* <DailyFact /> */}
          <HomePosts />
          <HomeFaculties />
          <HomeProducts />

          {/* <HomeEmailSubscription /> */}
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}
