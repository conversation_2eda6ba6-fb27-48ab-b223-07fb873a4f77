import FacultyPositionPinIcon from '@/assets/icons/faculty-position-pin.svg'
import MobileSearchIcon from '@/assets/icons/mobile-searching.svg'
import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import SearchStethoscopeIcon from '@/assets/icons/search-stethoscope-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useSheetActions } from '@/contexts/SheetContext/SheetContext'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useGetInfiniteSearchListKeywords } from '@/hooks/query/keyword/useGetInfiniteSearchListKeywords'
import { APP_ROUTES } from '@/routes/appRoutes'
import { LocalizeField } from '@/types/global.type'
import { Keyword } from '@/types/keyword.type'
import { BottomSheetFlatList, BottomSheetTextInput } from '@gorhom/bottom-sheet'
import { LinearGradient } from 'expo-linear-gradient'
import { LinkProps, useRouter } from 'expo-router'
import { debounce } from 'lodash-es'
import { stringify } from 'qs-esm'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  ActivityIndicator,
  Keyboard,
  ListRenderItem,
  StyleSheet,
  TouchableHighlight,
  TouchableOpacity,
  View,
} from 'react-native'
import { TextInput as GestureTextInput } from 'react-native-gesture-handler'
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

type KeywordItem = Pick<Keyword, 'id' | 'name'>

const HOME_SEARCH_HEADER = [
  {
    title: 'MES-784',
    icon: <SearchStethoscopeIcon />,
  },
  {
    title: 'MES-749',
    icon: <FacultyPositionPinIcon />,
  },
  {
    title: 'MES-750',
    icon: <MobileSearchIcon />,
  },
]

interface AnimatedHeaderContentProps {
  onPress: () => void
}

const AnimatedHeaderContent = ({ onPress }: AnimatedHeaderContentProps) => {
  const { t } = useTranslation()
  const [currentIndex, setCurrentIndex] = useState(0)
  const opacity = useSharedValue(1)
  const translateY = useSharedValue(0)

  const currentHeaderItem = HOME_SEARCH_HEADER[currentIndex]

  useEffect(() => {
    const interval = setInterval(() => {
      // First: Fade out and slide up
      opacity.value = withTiming(0, { duration: 350, easing: Easing.ease })
      translateY.value = withTiming(-20, { duration: 350, easing: Easing.ease })

      // After fade out completes, change content and fade in from bottom with spring bounce
      setTimeout(() => {
        // Update index when content is completely hidden
        setCurrentIndex((prev) => (prev + 1) % HOME_SEARCH_HEADER.length)

        // Reset position to bottom for slide in effect
        translateY.value = 20

        // Fade in with timing and slide to center with spring bounce
        opacity.value = withTiming(1, { duration: 350, easing: Easing.ease })
        translateY.value = withSpring(0, {
          damping: 7,
          stiffness: 150,
          mass: 0.5,
        })
      }, 350)
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: opacity.value,
    transform: [{ translateY: translateY.value }],
  }))

  return (
    <View>
      <View>
        <View className="flex flex-row items-center justify-between gap-x-2">
          <Animated.View
            style={[
              animatedStyle,
              {
                flex: 1,
                maxWidth: 244,
                height: 70,
                justifyContent: 'center',
              },
            ]}
          >
            <Text size="heading7" variant="white" className="">
              {t(currentHeaderItem.title)}
            </Text>
          </Animated.View>

          <Animated.View style={[animatedStyle, { flexShrink: 0 }]}>
            {currentHeaderItem.icon}
          </Animated.View>
        </View>

        {/* Search Bar */}
        <TouchableHighlight
          onPress={onPress}
          underlayColor="transparent"
          accessibilityRole="button"
        >
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              gap: 8,
              backgroundColor: 'white',
              borderRadius: 8,
              marginTop: 12,
              overflow: 'hidden',
              padding: 12,
            }}
          >
            <SearchInputIcon width={18} height={18} />

            <View className="flex-1 overflow-hidden">
              <Text className="line-clamp-1" size="field1" variant="subdued">
                {t('MES-748')}
              </Text>
            </View>
          </View>
        </TouchableHighlight>
      </View>
    </View>
  )
}

export const HomeSearch = () => {
  const { t } = useTranslation()
  const { openCustomSheet } = useSheetActions()
  const searchInputRef = useRef<GestureTextInput>(null)
  const shouldFoccus = useRef(true)
  const handleOpenKeywordSheet = useCallback(() => {
    openCustomSheet({
      children: <KeywordSheet searchInputRef={searchInputRef} />,
      baseProps: {
        snapPoints: ['50%', '70%'],
        enableDynamicSizing: false,
        enableOverDrag: false,
        // onClose,
      },
      options: {
        // Only focus input when sheet is opened for the first time
        onChange: (index, _) => {
          if (index === 0 && shouldFoccus.current) {
            searchInputRef.current?.focus()
          }
          if (index === 1) {
            shouldFoccus.current = false
          }
        },
        // Reset shouldFocus when sheet is closed
        onClose: () => {
          shouldFoccus.current = true
          Keyboard.dismiss()
        },
      },
    })
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [t])

  return (
    <View className="mt-3 flex px-4 ">
      <LinearGradient
        colors={['#5BAEF3', '#1764E0']}
        start={{ x: 0.999, y: 1.046 }}
        end={{ x: 0.02, y: 0.085 }}
        style={{
          paddingHorizontal: 16,
          paddingVertical: 24,
          borderRadius: 12,
          overflow: 'hidden',
        }}
      >
        {/* Decorative circles */}
        <View style={styles.decorativeCircle1} />
        <View style={styles.decorativeCircle2} />
        <View style={styles.decorativeCircle3} />

        <AnimatedHeaderContent onPress={handleOpenKeywordSheet} />
      </LinearGradient>
    </View>
  )
}

interface KeywordSheetProps {
  searchInputRef: React.RefObject<GestureTextInput | null>
}

const KeywordSheet = ({ searchInputRef }: KeywordSheetProps) => {
  const { primaryLanguage, secondaryLanguage } = useAppLanguage()
  const { closeSheet } = useSheetActions()
  const router = useRouter()
  const insets = useSafeAreaInsets()
  const { t } = useTranslation()
  const [searchKeywordValue, setSearchKeywordValue] = useState('')
  const [searchInputValue, setSearchInputValue] = useState('')

  const {
    searchListKeywords,
    isGetSearchListKeywordsLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetInfiniteSearchListKeywords({
    params: {
      locale: 'all',
      limit: 20,
      searchValue: searchKeywordValue || '',
    },
    config: {
      staleTime: 5 * 60 * 1000,
    },
  })

  // Create debounced search function
  const debouncedSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchKeywordValue(value)
      }, 300),
    [],
  )
  useEffect(() => {
    return () => {
      debouncedSearch.cancel()
    }
  }, [debouncedSearch])
  // Handle input change with debounce
  const handleSearchInputChange = useCallback(
    (text: string) => {
      setSearchInputValue(text)
      debouncedSearch(text)
    },
    [debouncedSearch],
  )

  const flatKeywordItems = searchListKeywords?.pages.flatMap((page) => page.docs) || []
  const keyExtractor = useCallback((item: unknown) => (item as KeywordItem)?.id, [])
  const renderKeywordItem: ListRenderItem<KeywordItem> = useCallback(({ item }) => {
    const localizedName = item?.name as unknown as LocalizeField<string>
    const keywordParamFormat = stringify({
      q:
        `${localizedName[primaryLanguage as LocaleEnum]}` +
        (secondaryLanguage ? ` (${localizedName[secondaryLanguage as LocaleEnum]})` : ''),
    })
    return (
      <TouchableOpacity
        className="border-b border-custom-divider px-4 py-2"
        onPress={() => {
          closeSheet()
          router.push({
            pathname: APP_ROUTES.SEARCH_SUMMARY.path,
            params: {
              keyword: keywordParamFormat,
            },
          } as LinkProps['href'])
        }}
      >
        {localizedName[primaryLanguage as LocaleEnum] && (
          <Text size="body6">{localizedName[primaryLanguage as LocaleEnum]}</Text>
        )}
        {localizedName[secondaryLanguage as LocaleEnum] && (
          <Text size="body6" variant="subdued">
            {localizedName[secondaryLanguage as LocaleEnum]}
          </Text>
        )}
      </TouchableOpacity>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage])
  return (
    <View style={{ paddingBottom: insets.bottom }}>
      <View className="flex-col gap-y-2 pb-3">
        <View className="mx-4 flex-row items-center gap-x-2 overflow-hidden rounded-lg border border-custom-divider bg-white p-3">
          <SearchInputIcon width={18} height={18} />
          <BottomSheetTextInput
            placeholder={t('MES-562')}
            placeholderTextColor="#8B8C99"
            value={searchInputValue}
            onChangeText={handleSearchInputChange}
            style={{ flex: 1 }}
            className="p-0"
            ref={searchInputRef}
            onSubmitEditing={(e) => {
              const keywordParamFormat = stringify({
                q: e.nativeEvent.text,
              })

              closeSheet()
              router.push({
                pathname: APP_ROUTES.SEARCH_SUMMARY.path,
                params: {
                  keyword: keywordParamFormat,
                },
              } as LinkProps['href'])
            }}
          />
        </View>
      </View>

      {isGetSearchListKeywordsLoading && !isFetchingNextPage ? (
        <View className="items-center py-4">
          <ActivityIndicator size="small" />
        </View>
      ) : (
        <BottomSheetFlatList
          contentContainerStyle={{ paddingBottom: insets.bottom + 16 }}
          renderItem={renderKeywordItem}
          data={flatKeywordItems}
          keyExtractor={keyExtractor}
          onEndReached={handleEndReached}
          onEndReachedThreshold={0.3}
          initialNumToRender={20}
          maxToRenderPerBatch={20}
          windowSize={21}
          removeClippedSubviews={true}
          ListFooterComponent={() => {
            return isFetchingNextPage ? (
              <View className="items-center py-4">
                <ActivityIndicator />
              </View>
            ) : null
          }}
          ListEmptyComponent={() => {
            return !isGetSearchListKeywordsLoading && flatKeywordItems.length === 0 ? (
              <View className="items-center py-10">
                <Text size="body6">{t('MES-517')}</Text>
              </View>
            ) : null
          }}
        />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  decorativeCircle1: {
    position: 'absolute',
    width: 264.34,
    height: 264.34,
    left: -122,
    top: -92,
    borderWidth: 1,
    borderColor: 'rgba(141, 238, 230, 0.2)',
    borderRadius: 132.17,
    backgroundColor: 'transparent',
  },
  decorativeCircle2: {
    position: 'absolute',
    width: 95.02,
    height: 95.02,
    right: -47.51,
    top: 102,
    borderWidth: 1,
    borderColor: 'rgba(141, 238, 230, 0.1)',
    borderRadius: 47.51,
    backgroundColor: 'transparent',
  },
  decorativeCircle3: {
    position: 'absolute',
    width: 64,
    height: 64,
    left: 67,
    top: -40,
    borderRadius: 32,
    backgroundColor: 'rgba(141, 238, 230, 0.1)',
  },
})
