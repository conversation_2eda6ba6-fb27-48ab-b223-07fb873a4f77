import PhoneIcon from '@/assets/icons/phone-icon.svg'
import { Button } from '@/components/ui/Button/Button'
import { Text } from '@/components/ui/Text/Text'
import * as Haptics from 'expo-haptics'
import { openBrowserAsync } from 'expo-web-browser'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
export const TranslatorContact = () => {
  const { t } = useTranslation()
  const handleCompanyPress = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    try {
      await openBrowserAsync('https://www.facebook.com/share/16b1uqLbD6/?mibextid=wwXIfr')
    } catch (error) {
      console.error('Failed to open browser:', error)
    }
  }

  return (
    <View className="my-2 px-4">
      <View
        className="flex flex-row items-center justify-between gap-x-2 rounded-lg bg-primary-50 p-4"
        style={{
          boxShadow: '2px 2px 8px 0px #00000014',
        }}
      >
        <Text size="body6" variant="primary">
          {t('MES-633')}
        </Text>
        <Button className="min-w-[90px]" onPress={handleCompanyPress}>
          <PhoneIcon width={16} height={16} />
          <Text size="button5" variant="white">
            {t('MES-634')}
          </Text>
        </Button>
      </View>
    </View>
  )
}
