import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import colors from '@/styles/_colors'
import { useLocalSearchParams } from 'expo-router'
import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, SafeAreaView, View } from 'react-native'
import Svg, { Path } from 'react-native-svg'
import { MedicalDocumentItem } from '../../components/MedicalDocumentItem/MedicalDocumentItem'
import { useGetInfiniteMedicalDocuments } from '../../hooks/useGetInfiniteMedicalDocuments'
import { useGetMedicalDocumentCategory } from '../../hooks/useGetMedicalDocumentCategory'
import { MedicalDocument } from '../../types'

export const MedicalDocumentsList = () => {
  const { id, hospitalId } = useLocalSearchParams()
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const [refreshing, setRefreshing] = useState(false)

  const {
    medicalDocumentCategory,
    isGetMedicalDocumentCategoryLoading,
    isFetching,
    refetch: refetchCategory,
  } = useGetMedicalDocumentCategory({
    id: id as string,
    params: {
      locale: primaryLanguage,
    },
    useQueryOptions: {
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
    },
  })

  const {
    medicalDocuments,
    isGetMedicalDocumentsLoading,
    isGetMedicalDocumentsError,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    refetch,
  } = useGetInfiniteMedicalDocuments({
    params: {
      locale: primaryLanguage,
      where: {
        categories: {
          in: [id as string],
        },
        hospital: {
          equals: hospitalId ? (hospitalId as string) : undefined,
        },
      },
      depth: 2,
      limit: 10,
      select: {
        id: true,
        name: true,
        file: true,
        createdAt: true,
        updatedAt: true,
        fileType: true,
        // tags: true,
      },
      // populate: {
      //   keywords: {
      //     name: true,
      //   },
      // },
    },
    config: {
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
    },
  })

  // Flatten all medical documents from all pages
  const allMedicalDocuments = useMemo(() => {
    if (!medicalDocuments?.pages) return []
    return medicalDocuments.pages.flatMap((page) => page?.docs || [])
  }, [medicalDocuments])

  const totalMedicalDocuments = useMemo(() => {
    return medicalDocuments?.pages?.[0]?.totalDocs || 0
  }, [medicalDocuments])

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await Promise.all([refetch(), refetchCategory()])
    } finally {
      setRefreshing(false)
    }
  }, [refetch, refetchCategory])

  // Handle load more
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage])

  // Check if we should show skeleton (initial load or refresh, but not fetch next page)
  const shouldShowSkeleton = (isGetMedicalDocumentsLoading || refreshing) && !isFetchingNextPage

  // Check if category header should show skeleton
  const shouldShowCategorySkeleton = isGetMedicalDocumentCategoryLoading || refreshing || isFetching

  // Render skeleton loading for category header
  const renderCategoryHeaderSkeleton = useCallback(() => {
    return (
      <View className="flex flex-col gap-y-4 p-4">
        <View className="flex flex-row items-center gap-x-2">
          <Skeleton className="h-6 w-6 rounded" />
          <Skeleton className="h-6 w-2/3 rounded" />
        </View>
        <View>
          <Skeleton className="mb-1 h-4 w-1/4 rounded" />
          <Skeleton className="h-4 w-full rounded" />
          <Skeleton className="mt-1 h-4 w-3/4 rounded" />
        </View>
        <View>
          <Skeleton className="mb-1 h-4 w-1/4 rounded" />
          <Skeleton className="h-4 w-full rounded" />
          <Skeleton className="mt-1 h-4 w-3/4 rounded" />
        </View>
      </View>
    )
  }, [])

  // Render skeleton loading for documents
  const renderDocumentSkeletons = useCallback(() => {
    return (
      <View className="flex flex-col gap-y-3">
        {new Array(8).fill(0).map((_, index) => (
          <View
            key={index}
            className="flex flex-row items-center gap-x-3 rounded-lg border border-gray-200 bg-white p-4"
          >
            {/* File Icon Skeleton */}
            <View className="flex-shrink-0">
              <Skeleton className="h-9 w-9 rounded" />
            </View>
            {/* Document Info Skeleton */}
            <View className="flex-1 flex-col gap-y-1">
              <Skeleton className="h-4 w-3/4 rounded" />
              <Skeleton className="h-3 w-1/3 rounded" />
            </View>
          </View>
        ))}
      </View>
    )
  }, [])

  // Render list header with category details
  const renderListHeader = useCallback(() => {
    if (shouldShowCategorySkeleton) {
      return renderCategoryHeaderSkeleton()
    }

    return (
      <View className="flex flex-col gap-y-4 py-4">
        <View className="flex flex-row items-center gap-x-2">
          <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
            <Path
              d="M21.0169 7.99175C21.4148 8.55833 20.9405 9.25 20.2482 9.25H3C2.44772 9.25 2 8.80228 2 8.25V6.42C2 3.98 3.98 2 6.42 2H8.74C10.37 2 10.88 2.53 11.53 3.4L12.93 5.26C13.24 5.67 13.28 5.72 13.86 5.72H16.65C18.4546 5.72 20.0516 6.61709 21.0169 7.99175Z"
              fill={medicalDocumentCategory?.color || colors.primary[500]}
            />
            <Path
              d="M20.9834 10.7497C21.5343 10.7497 21.9815 11.1954 21.9834 11.7463L22 16.65C22 19.6 19.6 22 16.65 22H7.35C4.4 22 2 19.6 2 16.65V11.75C2 11.1977 2.44771 10.75 2.99999 10.75L20.9834 10.7497Z"
              fill={medicalDocumentCategory?.color || colors.primary[500]}
            />
          </Svg>
          <Text size="heading8" variant="default">
            {medicalDocumentCategory?.name}
          </Text>
        </View>
        {medicalDocumentCategory?.description && (
          <View>
            <Text size="body7" variant="subdued">
              {t('MES-801')}
            </Text>

            <Text size="body7" variant="default">
              {medicalDocumentCategory?.description}
            </Text>
          </View>
        )}

        {!isGetMedicalDocumentsLoading && totalMedicalDocuments > 0 && (
          <Text size="body7" variant="subdued">
            {t('MES-802')} ({totalMedicalDocuments})
          </Text>
        )}
      </View>
    )
  }, [
    shouldShowCategorySkeleton,
    renderCategoryHeaderSkeleton,
    medicalDocumentCategory,
    t,
    isGetMedicalDocumentsLoading,
    totalMedicalDocuments,
  ])

  // Render document item
  const renderItem = useCallback(({ item }: { item: MedicalDocument }) => {
    return <MedicalDocumentItem medicalDocument={item} />
  }, [])

  // Render footer with loading indicator
  const renderListFooter = useCallback(() => {
    if (isFetchingNextPage) {
      return (
        <View className="items-center py-4">
          <ActivityIndicator size="small" />
        </View>
      )
    }
    return null
  }, [isFetchingNextPage])

  // Render empty component
  const renderEmptyComponent = useCallback(() => {
    if (isGetMedicalDocumentsError) {
      return (
        <View className="items-center py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }
    if (shouldShowSkeleton) {
      return null
    }

    return (
      <View className="items-center py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-803')}
        </Text>
      </View>
    )
  }, [isGetMedicalDocumentsError, shouldShowSkeleton, t])

  // Render item separator
  const renderSeparator = useCallback(() => {
    return <View className="h-3" />
  }, [])

  // Key extractor
  const keyExtractor = useCallback((item: MedicalDocument) => {
    return item.id
  }, [])

  // Show skeleton when loading initially or refreshing
  if (shouldShowSkeleton) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        <View className="px-4">
          {shouldShowCategorySkeleton ? renderCategoryHeaderSkeleton() : renderListHeader()}
          {renderDocumentSkeletons()}
        </View>
      </SafeAreaView>
    )
  }

  return (
    <FlatList
      data={allMedicalDocuments}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListHeaderComponent={renderListHeader}
      ListFooterComponent={renderListFooter}
      ListEmptyComponent={renderEmptyComponent}
      ItemSeparatorComponent={renderSeparator}
      contentContainerStyle={{
        paddingHorizontal: 16,
      }}
      showsVerticalScrollIndicator={false}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[colors.primary['500']]}
          tintColor={colors.primary['500']}
          progressBackgroundColor="#FFFFFF"
        />
      }
    />
  )
}
