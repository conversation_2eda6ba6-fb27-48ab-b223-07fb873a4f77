import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/Tabs/Tabs'
import { Text } from '@/components/ui/Text/Text'
import { variables } from '@/styles/_variables'
import { cn } from '@/utils/cn'
import * as Haptics from 'expo-haptics'
import { useLocalSearchParams } from 'expo-router'
import { useEffect, useMemo, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { ScrollView, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { PRODUCT_V2_TYPE_OPTIONS } from '../../constants'
import { ProductV2TypeEnum } from '../../enums'
import { useProductStore } from '../../stores/ProductStore'
import { ProductTabContent } from './ProductTabContent'

export const ProductTabs = () => {
  const { t } = useTranslation()
  const scrollViewRef = useRef<ScrollView>(null)
  const tabRefs = useRef<{ [key: string]: View | null }>({})
  const { categoryId, productType, categoryTitle, source } = useLocalSearchParams<{
    categoryId?: string
    productType?: string
    categoryTitle?: string
    source?: string
  }>()

  const { selectedProductType, setSelectedProductType, setSelectedCategory } = useProductStore(
    useShallow((state) => ({
      selectedProductType: state.selectedProductType,
      setSelectedProductType: state.setSelectedProductType,
      setSelectedCategory: state.setSelectedCategory,
    })),
  )

  // Set initial product type from query params if available
  useEffect(() => {
    if (
      productType &&
      Object.values(ProductV2TypeEnum).includes(productType as ProductV2TypeEnum)
    ) {
      setSelectedProductType(productType as ProductV2TypeEnum)
    }
  }, [productType, setSelectedProductType])

  // Auto-scroll to active tab
  useEffect(() => {
    if (scrollViewRef.current && tabRefs.current[selectedProductType]) {
      setTimeout(() => {
        const activeTabRef = tabRefs.current[selectedProductType]
        if (activeTabRef) {
          activeTabRef.measureLayout(
            scrollViewRef.current as any,
            (x) => {
              // Scroll to center the active tab
              const scrollX = Math.max(0, x - 50) // 50px offset for better visibility
              scrollViewRef.current?.scrollTo({
                x: scrollX,
                animated: true,
              })
            },
            () => {
              // Fallback to simple scroll if measureLayout fails
              console.warn('Failed to measure tab layout')
            },
          )
        }
      }, 100)
    }
  }, [selectedProductType])

  const tabTriggerClass = useMemo(() => {
    return {
      default:
        'self-start h-8 min-w-[67px] border border-transparent rounded-[60px] overflow-hidden bg-custom-neutral-80 px-3 py-1 text-center',
      active: 'border border-primary bg-white text-primary',
      'text-default': 'text-custom-text-subdued text-center',
      'text-active': 'text-primary-500',
    }
  }, [])

  const handleTabChange = (value: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
    setSelectedProductType(value as ProductV2TypeEnum)
  }

  return (
    <View className="flex-1 px-4" style={{ marginBottom: variables.layout.tabBarHeight + 20 }}>
      <Tabs value={selectedProductType} onValueChange={handleTabChange} className=" !px-0">
        <View>
          <ScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{ paddingHorizontal: 0 }}
          >
            <TabsList className=" flex flex-row gap-x-2  !px-0">
              {Object.values(PRODUCT_V2_TYPE_OPTIONS).map((type) => (
                <TabsTrigger
                  key={type.value}
                  value={type.value}
                  isActive={selectedProductType === type.value}
                  accessibilityLabel={t(`product.type.${type.value.toLowerCase()}`)}
                  accessibilityRole="tab"
                >
                  <View
                    ref={(ref) => {
                      tabRefs.current[type.value] = ref
                    }}
                    className={cn(
                      tabTriggerClass['default'],
                      selectedProductType === type.value &&
                        'border !border-primary !bg-white text-primary',
                    )}
                  >
                    <Text
                      size="body6"
                      className={cn(
                        tabTriggerClass['text-default'],
                        selectedProductType === type.value && tabTriggerClass['text-active'],
                      )}
                    >
                      {t(type.translationKey)}
                    </Text>
                  </View>
                </TabsTrigger>
              ))}
            </TabsList>
          </ScrollView>
        </View>

        {Object.values(PRODUCT_V2_TYPE_OPTIONS).map((type) => (
          <TabsContent key={type.value} value={type.value} className="mt-4">
            <ProductTabContent
              productType={type.value}
              initialCategoryId={categoryId}
              initialProductType={productType as ProductV2TypeEnum}
              initialCategoryTitle={categoryTitle}
              navigationSource={source}
              iShowProductListFilter={type.value !== ProductV2TypeEnum.MEDICAL_INSTRUMENT}
            />
          </TabsContent>
        ))}
      </Tabs>
    </View>
  )
}
