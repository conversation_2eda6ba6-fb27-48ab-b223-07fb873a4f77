import CloseIconDanger from '@/assets/icons/close-icon-danger.svg'
import FilterIcon from '@/assets/icons/filter-icon.svg'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { Product } from '@/features/product/types'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { APP_ROUTES } from '@/routes/appRoutes'
import colors from '@/styles/_colors'
import { LinkProps, useRouter } from 'expo-router'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, RefreshControl, TouchableOpacity, View } from 'react-native'
import { useShallow } from 'zustand/react/shallow'
import { ProductV2TypeEnum } from '../../enums'
import { useGetInfiniteFilteredProducts } from '../../hooks/query/product/useGetInfiniteFilteredProducts'
import { ProductFilterItem, ProductFilterType, useProductStore } from '../../stores/ProductStore'
import { FeaturedProducts } from '../FeaturedProducts/FeaturedProducts'
import { useOpenFilterProductBox } from '../FilterProductBox/FilterProductBox'
import { SelectedFilterBadge } from '../FilterProductBox/SelectedFilterBadge'
import { ProductCategories } from '../ProductCategories/ProductCategories'

import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { ProductItem } from '../ProductList/ProductItem'

interface ProductTabContentProps {
  productType: ProductV2TypeEnum
  initialCategoryId?: string
  initialProductType?: ProductV2TypeEnum
  initialCategoryTitle?: string
  navigationSource?: string
  iShowProductListFilter?: boolean
}

type ListItem =
  | { type: 'product_row'; products: Product[] }
  | { type: 'loading' }
  | { type: 'loading_skeleton' }

export const ProductTabContent = ({
  productType,
  initialCategoryId,
  initialProductType,
  initialCategoryTitle,
  navigationSource,
  iShowProductListFilter = true,
}: ProductTabContentProps) => {
  const { primaryLanguage } = useAppLanguage()
  const { t } = useTranslation()
  const router = useRouter()
  const [_, setHeaderHeight] = useState(0)
  const [refreshing, setRefreshing] = useState(false)
  const { productTabState, removeFilter, clearAllFilters, setSelectedCategory, goBackToRoot } =
    useProductStore(
      useShallow((state) => ({
        productTabState: state.productTabState,
        removeFilter: state.removeFilter,
        clearAllFilters: state.clearAllFilters,
        setSelectedCategory: state.setSelectedCategory,
        goBackToRoot: state.goBackToRoot,
      })),
    )

  // Handle initial category selection from query params
  useEffect(() => {
    if (initialCategoryId && initialProductType && productType === initialProductType) {
      // If navigating from home or external source, reset all filters and go to root first
      if (navigationSource === 'home') {
        // Clear all filters for this product type
        clearAllFilters(initialProductType)
        // Go back to root to clear any existing category selection
        goBackToRoot(initialProductType)
      }

      const placeholderCategory = {
        id: initialCategoryId,
        title: initialCategoryTitle || 'Loading...',
        type: initialProductType as 'MEDICINE' | 'DIETARY_SUPPLEMENT' | 'MEDICAL_INTRUMENT',
        icon: null,
        parent: null,
        lft: 0,
        rgt: 0,
        categoryLevel: 0,
        slug: null,
        slugLock: false,
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      }

      setSelectedCategory(initialProductType, placeholderCategory)

      // Clear search params to prevent this effect from running again on tab switches
      router.replace({
        pathname: APP_ROUTES.PRODUCTS.tabPath,
        params: undefined,
      } as LinkProps['href'])
    }
  }, [
    initialCategoryId,
    initialProductType,
    initialCategoryTitle,
    navigationSource,
    productType,
    setSelectedCategory,
    clearAllFilters,
    goBackToRoot,
    router,
  ])

  const {
    filteredProducts,
    isGetFilteredProductsLoading,
    isGetFilteredProductsError,
    fetchNextPage,
    hasNextPage,
    isGetFilteredProductsFetchingNextPage,
    isRefetching,
    refetch,
  } = useGetInfiniteFilteredProducts({
    config: {
      staleTime: 5 * 60 * 1000,
    },
    params: {
      locale: primaryLanguage,
      limit: 8,
      categories: productTabState[productType].selectedCategory
        ? [productTabState[productType].selectedCategory]
        : [],
      where: {
        and: [
          {
            type: {
              equals: productType,
            },
          },
          {
            ageGroups: {
              in:
                productTabState[productType].filters[ProductFilterType.AGE_GROUP]?.map(
                  (filter) => filter.id,
                ) || undefined,
            },
          },
          {
            medicineType: {
              in:
                productTabState[productType].filters[ProductFilterType.MEDICINE_TYPE]?.map(
                  (filter) => filter.id,
                ) || undefined,
            },
          },
        ],
      },
    },
  })

  const { handleOpenFilterProductBox } = useOpenFilterProductBox(productType)

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      await refetch()
    } finally {
      setRefreshing(false)
    }
  }, [refetch])

  // Flatten all products from all pages
  const allProducts = useMemo(() => {
    if (!filteredProducts?.pages) return []
    return filteredProducts.pages.flatMap((page) => page?.docs || [])
  }, [filteredProducts])

  // Group products into pairs for 2-column layout
  const productRows = useMemo(() => {
    const rows: Product[][] = []
    for (let i = 0; i < allProducts.length; i += 2) {
      rows.push(allProducts.slice(i, i + 2))
    }
    return rows
  }, [allProducts])

  // Create data array for FlatList (only products-related items)
  const data = useMemo((): ListItem[] => {
    const items: ListItem[] = []

    // Show loading skeletons if initial loading or refreshing
    if (isGetFilteredProductsLoading || isRefetching || refreshing) {
      items.push({ type: 'loading_skeleton' })
    } else {
      // Products in rows (2 columns)
      productRows.forEach((row) => {
        items.push({ type: 'product_row', products: row })
      })

      // Loading indicator for pagination
      if (isGetFilteredProductsFetchingNextPage) {
        items.push({ type: 'loading' })
      }
    }

    return items
  }, [
    allProducts,
    productRows,
    isGetFilteredProductsFetchingNextPage,
    isGetFilteredProductsLoading,
    isRefetching,
    refreshing,
  ])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetFilteredProductsFetchingNextPage) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetFilteredProductsFetchingNextPage, fetchNextPage])

  const handleHeaderLayout = useCallback((event: any) => {
    const { height } = event.nativeEvent.layout
    setHeaderHeight(height)
  }, [])

  const totalProducts = useMemo(() => {
    const total = filteredProducts?.pages[0]?.totalDocs || 0

    return total > 99 ? '99+' : total
  }, [filteredProducts])

  const activeFilters = useMemo(() => {
    return Object.values(productTabState[productType as ProductV2TypeEnum].filters).flat()
  }, [productTabState, productType])

  const hasActiveFilters = useMemo(() => {
    return productTabState[productType as ProductV2TypeEnum].hasActiveFilters
  }, [productTabState, productType])

  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    switch (item.type) {
      case 'product_row':
        return (
          <View className="mb-3 flex  w-full">
            <View className="flex flex-row gap-3">
              {item.products.map((product) => (
                <ProductItem key={product.id} product={product} />
              ))}
              {/* Fill empty space if odd number of products */}
              {item.products.length === 1 && <View className="flex-1" />}
            </View>
          </View>
        )

      case 'loading':
        return (
          <View className="items-center py-4">
            <ActivityIndicator size="small" />
          </View>
        )

      case 'loading_skeleton':
        return (
          <View className="flex flex-1 flex-col gap-3" style={{ gap: 20 }}>
            <View className="flex flex-row gap-3 ">
              {[...Array.from({ length: 2 }).fill(1)].map((_, index) => (
                <View key={index} className="flex aspect-square  w-1/2 flex-1 flex-col gap-y-2 ">
                  <Skeleton className=" flex-1 rounded-lg bg-gray-200" />
                  <Skeleton className="h-4 w-2/3 rounded-lg bg-gray-200" />
                </View>
              ))}
            </View>
            <View className="flex flex-row gap-3 ">
              {[...Array.from({ length: 2 }).fill(1)].map((_, index) => (
                <View key={index} className="flex aspect-square  w-1/2 flex-1 flex-col gap-y-2 ">
                  <Skeleton className=" flex-1 rounded-lg bg-gray-200" />
                  <Skeleton className="h-4 w-2/3 rounded-lg bg-gray-200" />
                </View>
              ))}
            </View>
            <View className="flex flex-row gap-3 ">
              {[...Array.from({ length: 2 }).fill(1)].map((_, index) => (
                <View key={index} className="flex aspect-square  w-1/2 flex-1 flex-col gap-y-2 ">
                  <Skeleton className=" flex-1 rounded-lg bg-gray-200" />
                  <Skeleton className="h-4 w-2/3 rounded-lg bg-gray-200" />
                </View>
              ))}
            </View>
          </View>
        )

      default:
        return null
    }
  }, [])

  const renderHeaderComponent = useCallback(() => {
    return (
      <View onLayout={handleHeaderLayout} className="gap-y-3 ">
        {/* Categories section */}
        <View className="">
          <ProductCategories
            productType={productType as ProductV2TypeEnum}
            isRefreshing={refreshing}
          />
        </View>

        {/* Featured products section */}
        <View className="">
          <FeaturedProducts
            productType={productType as ProductV2TypeEnum}
            isRefreshing={refreshing}
          />
        </View>

        {/* Products header */}
        <View className="flex flex-row items-center justify-between">
          <Text size="body3" className="font-medium">
            {t('MES-660')} {!isGetFilteredProductsLoading && `(${totalProducts})`}
          </Text>
          {iShowProductListFilter && (
            <TouchableOpacity
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              onPress={() => handleOpenFilterProductBox()}
              className="relative flex flex-row items-center gap-x-2 pr-1"
            >
              <Text size="body6" variant="primary">
                {t('MES-481')}
              </Text>

              <FilterIcon width={16} height={16} />
              {hasActiveFilters && (
                <View className="absolute  -top-0.5 right-[0.5px] size-2 rounded-full bg-custom-danger-600/80 opacity-80" />
              )}
            </TouchableOpacity>
          )}
        </View>

        {/* Selected filters section */}
        <View className="mb-3 ">
          {activeFilters?.length > 0 && (
            <View className="flex flex-col gap-y-3">
              <View className="flex flex-row flex-wrap gap-2">
                {activeFilters?.map((filter: ProductFilterItem) => (
                  <SelectedFilterBadge
                    key={`${filter.type}-${filter.id}`}
                    filterItem={filter}
                    onClearFilter={(item) =>
                      removeFilter(productType, item.type as ProductFilterType, item.id)
                    }
                  />
                ))}
                <TouchableOpacity
                  onPress={() => clearAllFilters(productType)}
                  className="flex flex-row items-center gap-x-1"
                >
                  <Text size="body8" variant="error">
                    {t('MES-709')}
                  </Text>
                  <View className="-ml-1">
                    <CloseIconDanger width={20} height={20} />
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </View>
    )
  }, [
    handleHeaderLayout,
    refreshing,
    t,
    totalProducts,
    isGetFilteredProductsLoading,
    activeFilters,
    productType,
    hasActiveFilters,
    removeFilter,
    clearAllFilters,
  ])

  const renderEmptyComponent = useCallback(() => {
    if (isGetFilteredProductsError) {
      return (
        <View className="items-center  py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-197')}
          </Text>
        </View>
      )
    }

    return (
      <View className="flex flex-col items-center justify-center gap-y-2  py-8">
        <EmptyBoxIcon />
        <Text size="body6" variant="default">
          {t('MES-717')}
        </Text>
      </View>
    )
  }, [isGetFilteredProductsError])

  const keyExtractor = useCallback(
    (item: ListItem, index: number) => {
      if (item.type === 'product_row') {
        return `product-row-${item.products.map((p) => p.id).join('-')}-${index}-${productType}`
      }
      return `${item.type}-${index}-${productType}`
    },
    [productType],
  )

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      ListHeaderComponent={renderHeaderComponent}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.1}
      ListEmptyComponent={renderEmptyComponent}
      showsVerticalScrollIndicator={false}
      removeClippedSubviews={false}
      maxToRenderPerBatch={10}
      windowSize={10}
      initialNumToRender={5}
      contentContainerStyle={{ paddingBottom: 20 }}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={[colors.primary['500']]}
          tintColor={colors.primary['500']}
          progressBackgroundColor="#FFFFFF"
        />
      }
    />
  )
}
