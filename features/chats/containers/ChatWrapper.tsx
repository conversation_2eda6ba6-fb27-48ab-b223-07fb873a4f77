'use client'
import { useAuthentication } from '@/hooks/auth/useAuthentication';
import { useAppLanguage } from '@/hooks/common/useAppLanguage';
import { Media } from '@/types/media.type';
import ChatWootWidget from '@chatwoot/react-native-widget';
import { router } from 'expo-router';
import React from 'react';
import { SafeAreaView, StyleSheet } from 'react-native';

export default function ChatWrapper() {

  const { user: userResponse } = useAuthentication()

  const {primaryLanguage : locale} = useAppLanguage()

  const avatarMedia = userResponse?.avatar as Media
  const avatarURL = avatarMedia?.url || avatarMedia?.thumbnailURL || userResponse?.oauthAvatar || ''

  const user = {
    identifier: userResponse?.email,
    name: userResponse?.name,
    avatar_url: avatarURL,
    email: userResponse?.email,
    identifier_hash: '',
  };

  const customAttributes = { accountId: 1, pricingPlan: 'paid', status: 'active' };
  const websiteToken = process.env.EXPO_TOKEN_CHATWOOT ?? '';
  const baseUrl = process.env.EXPO_BASE_URL_CHATWOOT ?? '';
  const colorScheme = 'dark';


  console.log("websiteToken", websiteToken)
  console.log("baseUrl", baseUrl)

  return (
    <SafeAreaView style={styles.container}>
      <ChatWootWidget
        websiteToken={websiteToken}
        locale={locale ?? 'vi'}
        baseUrl={baseUrl}
        closeModal={() => {
          router.back()
        }}
        isModalVisible={true} 
        user={user}
        customAttributes={customAttributes}
        colorScheme={colorScheme}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
});
