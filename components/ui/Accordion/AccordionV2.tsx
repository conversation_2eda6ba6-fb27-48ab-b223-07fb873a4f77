import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg'
import { cn } from '@/utils/cn'
import { cva, type VariantProps } from 'class-variance-authority'
import React, { createContext, useContext, useMemo, useState } from 'react'
import { Text, TouchableOpacity, View, ViewStyle } from 'react-native'
import Animated, { useAnimatedStyle, useSharedValue, withTiming } from 'react-native-reanimated'

// Types
interface AccordionContextValue {
  variant: 'default' | 'ghost'
  size: 'sm' | 'md' | 'lg'
  type: 'single' | 'multiple'
  openItems: string[]
  toggleItem: (value: string) => void
}

interface AccordionItemContextValue {
  value: string
  isOpen: boolean
  disabled: boolean
  toggle: () => void
}

// Context
const AccordionContext = createContext<AccordionContextValue | null>(null)
const AccordionItemContext = createContext<AccordionItemContextValue | null>(null)

// Hooks
const useAccordion = () => {
  const context = useContext(AccordionContext)
  if (!context) {
    throw new Error('useAccordion must be used within an Accordion')
  }
  return context
}

const useAccordionItem = () => {
  const context = useContext(AccordionItemContext)
  if (!context) {
    throw new Error('useAccordionItem must be used within an AccordionItem')
  }
  return context
}

// Styles using CVA
const accordionVariants = cva('w-full', {
  variants: {
    variant: {
      default: ' overflow-hidden',
      ghost: '',
    },
    size: {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'md',
  },
})

const accordionItemVariants = cva('', {
  variants: {
    variant: {
      default: '',
      ghost: 'mb-2',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
})

const accordionTriggerVariants = cva(
  'flex flex-row justify-between items-center w-full text-left',
  {
    variants: {
      variant: {
        default: 'px-4 py-3',
        ghost: 'px-0 py-2 rounded-md',
      },
      size: {
        sm: 'py-2 px-3 text-sm',
        md: 'py-3 px-4 text-base',
        lg: 'py-4 px-5 text-lg',
      },
      disabled: {
        true: 'opacity-50',
        false: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'md',
      disabled: false,
    },
  },
)

const accordionContentVariants = cva('', {
  variants: {
    variant: {
      default: 'px-4 pb-3',
      ghost: 'px-0 pb-2',
    },
    size: {
      sm: 'text-sm px-3 pb-2',
      md: 'text-base px-4 pb-3',
      lg: 'text-lg px-5 pb-4',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'md',
  },
})

const accordionTitleVariants = cva('flex-1 font-medium text-gray-900', {
  variants: {
    size: {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
    },
  },
  defaultVariants: {
    size: 'md',
  },
})

const accordionIconVariants = cva('', {
  variants: {
    size: {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
    },
  },
  defaultVariants: {
    size: 'md',
  },
})

// Component Props
interface AccordionProps extends VariantProps<typeof accordionVariants> {
  children: React.ReactNode
  type?: 'single' | 'multiple'
  defaultValue?: string | string[]
  value?: string | string[]
  onValueChange?: (value: string | string[]) => void
  className?: string
  style?: ViewStyle
}

interface AccordionItemProps extends VariantProps<typeof accordionItemVariants> {
  children: React.ReactNode
  value: string
  disabled?: boolean
  className?: string
  style?: ViewStyle
}

interface AccordionTriggerProps extends VariantProps<typeof accordionTriggerVariants> {
  children: React.ReactNode
  className?: string
  style?: ViewStyle
  onPress?: () => void
}

interface AccordionContentProps extends VariantProps<typeof accordionContentVariants> {
  children: React.ReactNode
  className?: string
  style?: ViewStyle
}

interface AccordionTitleProps extends VariantProps<typeof accordionTitleVariants> {
  children: React.ReactNode
  className?: string
  style?: any
}

interface AccordionIconProps extends VariantProps<typeof accordionIconVariants> {
  children?: React.ReactNode
  className?: string
  style?: ViewStyle
}

// Default Chevron Icon Component
const ChevronDownIcon: React.FC<{ className?: string }> = ({ className }) => (
  <View className={cn('h-5 w-5 items-center justify-center', className)}>
    <Text className="text-lg leading-none text-gray-600">⌄</Text>
  </View>
)

// Components
const Accordion = React.forwardRef<View, AccordionProps>(
  (
    {
      children,
      variant = 'default',
      size = 'md',
      type = 'single',
      defaultValue,
      value,
      onValueChange,
      className,
      style,
      ...props
    },
    ref,
  ) => {
    const [internalOpenItems, setInternalOpenItems] = useState<string[]>(() => {
      if (defaultValue) {
        return Array.isArray(defaultValue) ? defaultValue : [defaultValue]
      }
      return []
    })

    const openItems = value ? (Array.isArray(value) ? value : [value]) : internalOpenItems

    const toggleItem = (itemValue: string) => {
      let newOpenItems: string[]

      if (type === 'single') {
        newOpenItems = openItems.includes(itemValue) ? [] : [itemValue]
      } else {
        newOpenItems = openItems.includes(itemValue)
          ? openItems.filter((item) => item !== itemValue)
          : [...openItems, itemValue]
      }

      if (!value) {
        setInternalOpenItems(newOpenItems)
      }

      onValueChange?.(type === 'single' ? newOpenItems[0] || '' : newOpenItems)
    }

    const contextValue = useMemo(
      () => ({
        variant: variant || 'default',
        size: size || 'md',
        type,
        openItems,
        toggleItem,
      }),
      [variant, size, type, openItems, toggleItem],
    )

    return (
      <AccordionContext.Provider value={contextValue}>
        <View
          ref={ref}
          className={cn(accordionVariants({ variant, size }), className)}
          style={style}
          {...props}
        >
          {children}
        </View>
      </AccordionContext.Provider>
    )
  },
)

const AccordionItem = React.forwardRef<View, AccordionItemProps>(
  ({ children, value, disabled = false, variant, className, style, ...props }, ref) => {
    const { variant: accordionVariant, openItems, toggleItem } = useAccordion()
    const isOpen = openItems.includes(value)

    const toggle = () => {
      if (!disabled) {
        toggleItem(value)
      }
    }

    const contextValue = useMemo(
      () => ({
        value,
        isOpen,
        disabled,
        toggle,
      }),
      [value, isOpen, disabled, toggle],
    )

    return (
      <AccordionItemContext.Provider value={contextValue}>
        <View
          ref={ref}
          className={cn(accordionItemVariants({ variant: variant || accordionVariant }), className)}
          style={style}
          {...props}
        >
          {children}
        </View>
      </AccordionItemContext.Provider>
    )
  },
)

const AccordionTrigger = React.forwardRef<
  React.ComponentRef<typeof TouchableOpacity>,
  AccordionTriggerProps
>(({ children, variant, size, className, style, disabled, onPress, ...props }, ref) => {
  const { variant: accordionVariant, size: accordionSize } = useAccordion()
  const { disabled: accordionDisabled, toggle } = useAccordionItem()

  const handlePress = () => {
    if (!accordionDisabled) {
      toggle()
      onPress?.()
    }
  }

  return (
    <TouchableOpacity
      ref={ref}
      onPress={handlePress}
      disabled={accordionDisabled}
      className={cn(
        accordionTriggerVariants({
          variant: variant || accordionVariant,
          size: size || accordionSize,
          disabled: accordionDisabled,
        }),
        className,
      )}
      style={style}
      {...props}
    >
      {children}
    </TouchableOpacity>
  )
})

const AccordionContent = React.forwardRef<View, AccordionContentProps>(
  ({ children, variant, size, className, style, ...props }, ref) => {
    const { variant: accordionVariant, size: accordionSize } = useAccordion()
    const { isOpen } = useAccordionItem()

    // Simple show/hide without animation
    if (!isOpen) {
      return null
    }

    return (
      <View
        ref={ref}
        className={cn(
          accordionContentVariants({
            variant: variant || accordionVariant,
            size: size || accordionSize,
          }),
          className,
        )}
        style={style}
        {...props}
      >
        {children}
      </View>
    )
  },
)

const AccordionTitle = React.forwardRef<Text, AccordionTitleProps>(
  ({ children, size, className, style, ...props }, ref) => {
    const { size: accordionSize } = useAccordion()

    return (
      <Text
        ref={ref}
        className={cn(accordionTitleVariants({ size: size || accordionSize }), className)}
        style={style}
        {...props}
      >
        {children}
      </Text>
    )
  },
)

const AccordionIcon = React.forwardRef<View, AccordionIconProps>(
  ({ children, size, className, style, ...props }, ref) => {
    const { size: accordionSize } = useAccordion()
    const { isOpen } = useAccordionItem()

    const rotation = useSharedValue(0)

    React.useEffect(() => {
      rotation.value = withTiming(isOpen ? 180 : 0, { duration: 200 })
    }, [isOpen])

    const animatedStyle = useAnimatedStyle(() => ({
      transform: [{ rotate: `${rotation.value}deg` }],
    }))

    return (
      <Animated.View style={animatedStyle}>
        <View
          ref={ref}
          className={cn(accordionIconVariants({ size: size || accordionSize }), className)}
          style={style}
          {...props}
        >
          {children || <ArrowDownIcon width={18} height={18} />}
        </View>
      </Animated.View>
    )
  },
)

// Display names
Accordion.displayName = 'Accordion'
AccordionItem.displayName = 'AccordionItem'
AccordionTrigger.displayName = 'AccordionTrigger'
AccordionContent.displayName = 'AccordionContent'
AccordionTitle.displayName = 'AccordionTitle'
AccordionIcon.displayName = 'AccordionIcon'

export {
  Accordion,
  AccordionContent,
  AccordionIcon,
  AccordionItem,
  AccordionTitle,
  AccordionTrigger,
  type AccordionContentProps,
  type AccordionIconProps,
  type AccordionItemProps,
  type AccordionProps,
  type AccordionTitleProps,
  type AccordionTriggerProps,
}
