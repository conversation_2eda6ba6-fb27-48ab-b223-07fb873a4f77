import colors from '@/styles/_colors'
import React from 'react'
import {
  Text as RNText,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle,
} from 'react-native'

// Check icon component
const CheckIcon = ({ size = 16, color = '#ffffff' }: { size?: number; color?: string }) => (
  <View
    style={{
      width: size,
      height: size,
      alignItems: 'center',
      justifyContent: 'center',
    }}
  >
    <View
      style={{
        width: size * 0.6,
        height: size * 0.3,
        borderBottomWidth: 2,
        borderLeftWidth: 2,
        borderColor: color,
        transform: [{ rotate: '-45deg' }],
        marginTop: -size * 0.1,
      }}
    />
  </View>
)

// Checkbox variants
const checkboxVariants = {
  size: {
    sm: {
      container: 'w-4 h-4',
      containerStyle: { width: 16, height: 16 } as ViewStyle,
      iconSize: 12,
    },
    md: {
      container: 'w-5 h-5',
      containerStyle: { width: 20, height: 20 } as ViewStyle,
      iconSize: 14,
    },
    lg: {
      container: 'w-6 h-6',
      containerStyle: { width: 24, height: 24 } as ViewStyle,
      iconSize: 16,
    },
  },
}

// Base checkbox styles
const getCheckboxStyles = (
  checked: boolean,
  disabled: boolean,
  size: keyof typeof checkboxVariants.size,
) => {
  const baseStyle: ViewStyle = {
    ...checkboxVariants.size[size].containerStyle,
    borderWidth: 2,
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: checked ? colors.primary[500] : '#d1d5db',
    backgroundColor: checked ? colors.primary[500] : 'transparent',
    opacity: disabled ? 0.5 : 1,
  }

  return baseStyle
}

const getLabelStyles = (disabled: boolean, size: keyof typeof checkboxVariants.size): TextStyle => {
  const fontSize = size === 'sm' ? 14 : size === 'md' ? 16 : 18

  return {
    fontSize,
    color: disabled ? '#9ca3af' : '#374151',
    marginLeft: 8,
    flex: 1,
  }
}

// Checkbox component props
export interface CheckboxProps extends Omit<TouchableOpacityProps, 'onPress'> {
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  disabled?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
  children?: React.ReactNode
}

// Main Checkbox component
export const Checkbox = React.forwardRef<
  React.ComponentRef<typeof TouchableOpacity>,
  CheckboxProps
>(
  (
    {
      checked = false,
      onCheckedChange,
      disabled = false,
      size = 'md',
      className,
      children,
      style,
      ...props
    },
    ref,
  ) => {
    const handlePress = () => {
      if (!disabled && onCheckedChange) {
        onCheckedChange(!checked)
      }
    }

    const checkboxStyle = getCheckboxStyles(checked, disabled, size)
    const iconSize = checkboxVariants.size[size].iconSize

    return (
      <TouchableOpacity
        ref={ref}
        style={[
          {
            flexDirection: 'row',
            alignItems: 'center',
            opacity: disabled ? 0.5 : 1,
          },
          style,
        ]}
        onPress={handlePress}
        disabled={disabled}
        activeOpacity={0.7}
        {...props}
      >
        <View style={checkboxStyle}>
          {checked && <CheckIcon size={iconSize} color="#ffffff" />}
        </View>
        {children && <RNText style={getLabelStyles(disabled, size)}>{children}</RNText>}
      </TouchableOpacity>
    )
  },
)

// Checkbox with label component for easier usage
export interface CheckboxWithLabelProps extends CheckboxProps {
  label: string
  labelStyle?: TextStyle
}

export const CheckboxWithLabel = React.forwardRef<
  React.ComponentRef<typeof TouchableOpacity>,
  CheckboxWithLabelProps
>(({ label, labelStyle, ...props }, ref) => {
  return (
    <Checkbox ref={ref} {...props}>
      <RNText style={labelStyle}>{label}</RNText>
    </Checkbox>
  )
})

// Checkbox group for managing multiple checkboxes
export interface CheckboxGroupProps {
  value?: string[]
  onValueChange?: (value: string[]) => void
  disabled?: boolean
  children: React.ReactNode
  className?: string
}

export const CheckboxGroup = ({
  value = [],
  onValueChange,
  disabled = false,
  children,
  className,
}: CheckboxGroupProps) => {
  const handleCheckboxChange = (itemValue: string, checked: boolean) => {
    if (!onValueChange) return

    if (checked) {
      onValueChange([...value, itemValue])
    } else {
      onValueChange(value.filter((v) => v !== itemValue))
    }
  }

  return (
    <View className={className}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement<CheckboxItemProps>(child) && child.props.value) {
          return React.cloneElement(child, {
            ...child.props,
            checked: value.includes(child.props.value),
            onCheckedChange: (checked: boolean) => handleCheckboxChange(child.props.value, checked),
            disabled: disabled || child.props.disabled,
          } as CheckboxItemProps)
        }
        return child
      })}
    </View>
  )
}

// Individual checkbox item for use within CheckboxGroup
export interface CheckboxItemProps extends Omit<CheckboxProps, 'checked' | 'onCheckedChange'> {
  value: string
  label?: string
}

export const CheckboxItem = React.forwardRef<
  React.ComponentRef<typeof TouchableOpacity>,
  CheckboxItemProps
>(({ value, label, children, ...props }, ref) => {
  return (
    <Checkbox ref={ref} {...props}>
      {label || children}
    </Checkbox>
  )
})

// Set display names
Checkbox.displayName = 'Checkbox'
CheckboxWithLabel.displayName = 'CheckboxWithLabel'
CheckboxGroup.displayName = 'CheckboxGroup'
CheckboxItem.displayName = 'CheckboxItem'

// Export all components
export default Checkbox
