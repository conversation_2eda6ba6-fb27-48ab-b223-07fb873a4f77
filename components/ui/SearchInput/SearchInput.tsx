import ClearInputIcon from '@/assets/icons/clear-input-icon.svg'
import SearchInputIcon from '@/assets/icons/search-input-icon.svg'
import { TextInput } from '@/components/ui/TextInput/TextInput'
import colors from '@/styles/_colors'
import { cn } from '@/utils/cn'
import { forwardRef, useImperativeHandle, useRef, useState } from 'react'
import {
  ColorValue,
  TextInput as RNTextInput,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native'

export interface SearchInputProps {
  placeholder?: string
  value?: string
  onChangeText?: (text: string) => void
  onSubmitEditing?: () => void
  onClear?: () => void
  className?: string
  wrapperClassName?: string
  style?: ViewStyle
  autoFocus?: boolean
  placeholderTextColor?: string
  defaultValue?: string
}

export interface SearchInputRef {
  focus: () => void
  blur: () => void
  clear: () => void
}

export const SearchInput = forwardRef<SearchInputRef, SearchInputProps>(
  (
    {
      placeholder,
      value: controlledValue,
      onChangeText,
      onSubmitEditing,
      onClear,
      className,
      wrapperClassName,
      style,
      autoFocus = false,
      placeholderTextColor = '#8B8C99',
      defaultValue,
    },
    ref,
  ) => {
    const [internalValue, setInternalValue] = useState(defaultValue || '')
    const searchInputRef = useRef<RNTextInput>(null)

    const searchValue = controlledValue !== undefined ? controlledValue : internalValue
    const isControlled = controlledValue !== undefined

    const handleSearchInputChange = (text: string) => {
      if (!isControlled) {
        setInternalValue(text)
      }
      onChangeText?.(text)
    }

    const handleClearSearchInput = () => {
      if (!isControlled) {
        setInternalValue('')
      }
      onClear?.()
    }

    const handleSubmitEditing = () => {
      onSubmitEditing?.()
    }

    useImperativeHandle(ref, () => ({
      focus: () => searchInputRef.current?.focus(),
      blur: () => searchInputRef.current?.blur(),
      clear: handleClearSearchInput,
    }))

    return (
      <View
        className={cn(
          'flex-1 flex-row items-center gap-x-2 overflow-hidden rounded-lg border border-custom-divider bg-white px-3',
          wrapperClassName,
        )}
        style={[
          {
            borderColor: searchValue
              ? (colors.primary[500] as unknown as ColorValue)
              : (colors.divider['border'] as unknown as ColorValue),
          },
          style,
        ]}
      >
        <SearchInputIcon width={18} height={18} />
        <TextInput
          ref={searchInputRef}
          placeholder={placeholder}
          placeholderTextColor={placeholderTextColor}
          value={searchValue}
          onChangeText={handleSearchInputChange}
          onSubmitEditing={handleSubmitEditing}
          autoFocus={autoFocus}
          className={cn('p-0', className)}
          wrapperClassName={cn('flex-1 !px-0 !border-0 !border-none border-transparent')}
          defaultValue={defaultValue}
        />
        {searchValue && (
          <TouchableOpacity onPress={handleClearSearchInput}>
            <ClearInputIcon width={16} height={16} />
          </TouchableOpacity>
        )}
      </View>
    )
  },
)

SearchInput.displayName = 'SearchInput'
