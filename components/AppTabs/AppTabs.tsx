import { LinkProps, Tabs, useRouter, useSegments } from 'expo-router'
import React, { useMemo } from 'react'
import { Platform, View } from 'react-native'

import { APP_TABS } from '@/constants/navigation.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES } from '@/routes/appRoutes'
import colors, { primary } from '@/styles/_colors'
import { layout } from '@/styles/_variables'
import { cn } from '@/utils/cn'
import { useTranslation } from 'react-i18next'
import { SvgProps } from 'react-native-svg'
import { HapticTab } from '../HapticTab'
export const AppTabs = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { status, user } = useAuthentication()
  const segments = useSegments()

  // Memoize current tab calculation
  const currentTab = useMemo(() => {
    // segments structure: ['(tabs)', 'actual-tab-name'] or ['(tabs)'] for index
    if (segments.length <= 1 || segments[1] === undefined) {
      return 'index'
    }
    return segments[1]
  }, [segments])
  return (
    <Tabs
      initialRouteName="index"
      screenOptions={{
        animation: 'fade',
        headerShown: false,
        tabBarStyle: {
          ...Platform.select({
            ios: {
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -1.2 },
              shadowOpacity: 0.1,
              shadowRadius: 1.2,

              borderTopWidth: 0,
            },
            android: {
              elevation: 0,
              borderTopWidth: 0.5,
              borderTopColor: colors.divider.default,
            },
          }),
          height: layout.tabBarHeight,
          paddingBottom: 0,
        },
        tabBarLabelStyle: {
          fontSize: 11,
          fontFamily: 'Inter',
          fontWeight: '400',
          marginTop: 6,
          display: 'flex',
        },
        tabBarItemStyle: {
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        },
        tabBarActiveTintColor: primary[500],
        tabBarInactiveTintColor: '#8B8C99',
        tabBarButton: (props) => {
          return (
            <HapticTab {...props}>
              <View
                style={{
                  height: '100%',
                  width: '100%',
                  flexDirection: 'column',
                  alignItems: 'center',
                }}
              >
                {props.children}
              </View>
            </HapticTab>
          )
        },
      }}
    >
      {/* Tab 1: Từ điển */}
      <Tabs.Screen
        name={APP_TABS.MEDICAL_DICTIONARY.name}
        options={{
          title: t(APP_TABS.MEDICAL_DICTIONARY.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.MEDICAL_DICTIONARY.icon} />
          ),
        }}
      />
      {/* Tab 2: Sổ tay Y tế */}
      <Tabs.Screen
        name={APP_TABS.MEDICAL_HANDBOOK.name}
        options={{
          title: t(APP_TABS.MEDICAL_HANDBOOK.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.MEDICAL_HANDBOOK.icon} />
          ),
        }}
        listeners={{
          tabPress: (e) => {
            if (status === 'loading') {
              e.preventDefault()
              return
            }
            if (status === 'unauthorized' || !user) {
              e.preventDefault()
              router.push({
                pathname: APP_ROUTES.LOGIN.path,
                params: {
                  redirect: APP_TABS.MEDICAL_HANDBOOK.name,
                },
              } as LinkProps['href'])
            }
          },
        }}
      />
      {/* Tab 3: Home (Trang chủ) - Tab chính ở giữa */}
      <Tabs.Screen
        name={APP_TABS.HOME.name}
        options={{
          title: t(APP_TABS.HOME.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon
              focused={focused}
              color={color}
              icon={APP_TABS.HOME.icon}
              isHomeTab={true}
              isActive={currentTab === APP_TABS.HOME.name}
            />
          ),
        }}
      />
      {/* Tab 4: Sổ tay Thuốc */}
      <Tabs.Screen
        name={APP_TABS.PRODUCTS.name}
        options={{
          title: t(APP_TABS.PRODUCTS.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.PRODUCTS.icon} />
          ),
        }}
      />
      {/* Tab 5: Tin tức */}
      <Tabs.Screen
        name={APP_TABS.POSTS.name}
        options={{
          title: t(APP_TABS.POSTS.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.POSTS.icon} />
          ),
        }}
      />
      {/* Tab 6: Chat */}
      <Tabs.Screen
        name={APP_TABS.CHAT.name}
        options={{
          title: t(APP_TABS.CHAT.keyTranslate),
          tabBarIcon: ({ focused, color }) => (
            <TabBarIcon focused={focused} color={color} icon={APP_TABS.CHAT.icon} />
          ),
        }}
      />
    </Tabs>
  )
}

const TabBarIcon = ({
  color,
  icon,
  isHomeTab,
  isActive,
}: {
  focused: boolean
  color: string
  icon: React.ComponentType<SvgProps>
  isHomeTab?: boolean
  isActive?: boolean
}) => {
  const Icon = icon
  return (
    <View
      className={cn(
        ' relative flex h-8 w-8 flex-row items-center justify-center',
        isHomeTab && '-mt-4 h-14 w-14 rounded-full bg-primary p-2',
      )}
      // style={{
      //   ...Platform.select({
      //     ios: isHomeTab && {
      //       backgroundColor: primary[500],
      //       shadowColor: primary[500],
      //       shadowOffset: { width: 0, height: -2 },
      //       shadowOpacity: isActive ? 0.8 : 0,
      //       shadowRadius: 4,
      //       elevation: 8,
      //     },
      //   }),
      // }}
    >
      <Icon width={24} height={24} color={color} className="h-6 w-6" />
    </View>
  )
}
