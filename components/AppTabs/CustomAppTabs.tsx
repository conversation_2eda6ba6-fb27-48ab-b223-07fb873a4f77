import { LinkProps, useRouter, useSegments } from 'expo-router'
import { TabList, Tabs, TabSlot, TabTrigger } from 'expo-router/ui'
import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { Platform, View } from 'react-native'
import Animated, { LinearTransition, SlideInRight, SlideOutRight } from 'react-native-reanimated'
import { SvgProps } from 'react-native-svg'

import { HapticTab } from '@/components/HapticTab'
import { Text } from '@/components/ui/Text/Text'
import { APP_TABS } from '@/constants/navigation.constant'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES } from '@/routes/appRoutes'
import { primary } from '@/styles/_colors'
import { layout } from '@/styles/_variables'
import { cn } from '@/utils/cn'

export const CustomAppTabs = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { status, user } = useAuthentication()
  const segments = useSegments()

  // Memoize current tab calculation
  const currentTab = useMemo(() => {
    // segments structure: ['(tabs)', 'actual-tab-name'] or ['(tabs)'] for index
    if (segments.length <= 1 || segments[1] === undefined) {
      return 'index'
    }
    return segments[1]
  }, [segments])

  const handleTabPress = (tabName: string) => {
    if (status === 'loading') {
      return
    }

    // Check if authentication is required for certain tabs
    if (
      (tabName === 'medical-dictionary-tab' || tabName === 'medical-handbook-tab') &&
      (status === 'unauthorized' || !user)
    ) {
      router.push({
        pathname: APP_ROUTES.LOGIN.path,
        params: {
          redirect: tabName,
        },
      } as LinkProps['href'])
      return
    }
  }
  return (
    <Tabs>
      <Animated.View
        entering={SlideInRight.duration(300)}
        exiting={SlideOutRight.duration(200)}
        style={{ flex: 1 }}
        layout={LinearTransition.springify().duration(300)}
      >
        <TabSlot></TabSlot>
      </Animated.View>
      <TabList
        style={{
          height: layout.tabBarHeight,
          borderTopWidth: 0,
          backgroundColor: '#ffffff',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',

          ...Platform.select({
            ios: {
              shadowColor: '#000',
              shadowOffset: { width: 0, height: -1.2 },
              shadowOpacity: 0.1,
              shadowRadius: 1.2,
            },
            android: {
              elevation: 12,
            },
          }),
          paddingHorizontal: 4,
        }}
      >
        <TabTrigger name="medical-dictionary-tab" href="/medical-dictionary-tab" asChild>
          <HapticTab
            style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              width: '100%',
            }}
            onPress={() => handleTabPress('medical-dictionary-tab')}
          >
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <TabBarIcon
                focused={currentTab === APP_TABS.MEDICAL_DICTIONARY.name}
                color={currentTab === APP_TABS.MEDICAL_DICTIONARY.name ? primary[500] : '#8B8C99'}
                icon={APP_TABS.MEDICAL_DICTIONARY.icon}
              />
              <Text
                variant={currentTab === APP_TABS.MEDICAL_DICTIONARY.name ? 'primary' : 'subdued'}
                size="body9"
                numberOfLines={1}
              >
                {t(APP_TABS.MEDICAL_DICTIONARY.keyTranslate)}
              </Text>
            </View>
          </HapticTab>
        </TabTrigger>

        <TabTrigger name="medical-handbook-tab" href="/medical-handbook-tab" asChild>
          <HapticTab
            style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              width: '100%',
            }}
            onPress={() => handleTabPress('medical-handbook-tab')}
          >
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <TabBarIcon
                focused={currentTab === APP_TABS.MEDICAL_HANDBOOK.name}
                color={currentTab === APP_TABS.MEDICAL_HANDBOOK.name ? primary[500] : '#8B8C99'}
                icon={APP_TABS.MEDICAL_HANDBOOK.icon}
              />
              <Text
                variant={currentTab === APP_TABS.MEDICAL_HANDBOOK.name ? 'primary' : 'subdued'}
                size="body9"
                numberOfLines={1}
              >
                {t(APP_TABS.MEDICAL_HANDBOOK.keyTranslate)}
              </Text>
            </View>
          </HapticTab>
        </TabTrigger>

        <TabTrigger name={APP_TABS.HOME.name} href="/" asChild>
          <HapticTab
            style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              width: '100%',
            }}
          >
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <TabBarIcon
                focused={currentTab === APP_TABS.HOME.name}
                color={currentTab === APP_TABS.HOME.name ? 'white' : '#8B8C99'}
                icon={APP_TABS.HOME.icon}
                isHomeTab={true}
                isActive={currentTab === APP_TABS.HOME.name}
              />
              <Text
                variant={currentTab === APP_TABS.HOME.name ? 'primary' : 'subdued'}
                size="body9"
                numberOfLines={1}
              >
                {t(APP_TABS.HOME.keyTranslate)}
              </Text>
            </View>
          </HapticTab>
        </TabTrigger>

        <TabTrigger name="products-tab" href="/products-tab" asChild>
          <HapticTab
            style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              width: '100%',
            }}
          >
            <View
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexDirection: 'column',
              }}
            >
              <TabBarIcon
                focused={currentTab === 'products-tab'}
                color={currentTab === 'products-tab' ? primary[500] : '#8B8C99'}
                icon={APP_TABS.PRODUCTS.icon}
              />
              <Text
                variant={currentTab === 'products-tab' ? 'primary' : 'subdued'}
                size="body9"
                numberOfLines={1}
              >
                {t(APP_TABS.PRODUCTS.keyTranslate)}
              </Text>
            </View>
          </HapticTab>
        </TabTrigger>

        <TabTrigger name="posts-tab" href="/posts-tab" asChild>
          <HapticTab
            style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              width: '100%',
            }}
          >
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <TabBarIcon
                focused={currentTab === 'posts-tab'}
                color={currentTab === 'posts-tab' ? primary[500] : '#8B8C99'}
                icon={APP_TABS.POSTS.icon}
              />
              <Text
                variant={currentTab === 'posts-tab' ? 'primary' : 'subdued'}
                size="body9"
                numberOfLines={1}
              >
                {t(APP_TABS.POSTS.keyTranslate)}
              </Text>
            </View>
          </HapticTab>
        </TabTrigger>

        <TabTrigger name="chat-tab" href="/chat-tab" asChild>
          <HapticTab
            style={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              width: '100%',
            }}
          >
            <View
              style={{
                alignItems: 'center',
                justifyContent: 'center',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <TabBarIcon
                focused={currentTab === 'chat-tab'}
                color={currentTab === 'chat-tab' ? primary[500] : '#8B8C99'}
                icon={APP_TABS.CHAT.icon}
              />
              <Text
                variant={currentTab === 'chat-tab' ? 'primary' : 'subdued'}
                size="body9"
                numberOfLines={1}
              >
                {t(APP_TABS.CHAT.keyTranslate)}
              </Text>
            </View>
          </HapticTab>
        </TabTrigger>
      </TabList>
    </Tabs>
  )
}

const TabBarIcon = ({
  color,
  icon,
  isHomeTab,
  isActive,
}: {
  focused: boolean
  color: string
  icon: React.ComponentType<SvgProps>
  isHomeTab?: boolean
  isActive?: boolean
}) => {
  const Icon = icon
  return (
    <View
      className={cn(
        ' relative flex h-8 w-8 flex-row items-center justify-center',
        // isHomeTab && '-mt-8 h-14 w-14 rounded-full bg-primary p-2',
      )}
      // style={{
      //   ...Platform.select({
      //     ios: isHomeTab && {
      //       backgroundColor: primary[500],
      //       shadowColor: primary[500],
      //       shadowOffset: { width: 0, height: -2 },
      //       shadowOpacity: isActive ? 0.8 : 0,
      //       shadowRadius: 4,
      //       elevation: 8,
      //     },
      //   }),
      // }}
    >
      <Icon width={24} height={24} color={color} className="h-6 w-6" />
      {isHomeTab && (
        <View
          className="absolute -top-8 flex h-14 w-14 items-center justify-center rounded-full bg-primary"
          style={{
            ...Platform.select({
              ios: isHomeTab && {
                backgroundColor: primary[500],
                shadowColor: primary[500],
                shadowOffset: { width: 0, height: -2 },
                shadowOpacity: isActive ? 0.8 : 0,
                shadowRadius: 4,
                elevation: 8,
              },
            }),
          }}
        >
          <Icon width={24} height={24} color={color} className="h-6 w-6" />
        </View>
      )}
    </View>
  )
}
