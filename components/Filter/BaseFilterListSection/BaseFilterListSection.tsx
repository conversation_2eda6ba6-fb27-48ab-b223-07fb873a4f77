import ArrowDownIcon from '@/assets/icons/arrow-down-icon.svg'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/Accordion/Accordion'
import { SearchInput } from '@/components/ui/SearchInput/SearchInput'
import { Skeleton } from '@/components/ui/Skeleton/Skeleton'
import { Text } from '@/components/ui/Text/Text'
import { cn } from '@/utils/cn'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { FlatList, TouchableOpacity, View } from 'react-native'
interface BaseFilterListSectionProps<T> {
  title?: string
  data: T[]
  onSelectFilter?: (item: T) => void
  activeFilters?: string[]
  idKey: keyof T
  labelKey: keyof T
  numColumns?: number
  scrollEnabled?: boolean
  isLoading?: boolean
  isAccordion?: boolean
  children?: React.ReactNode
  withSearchInput?: boolean
}

export const BaseFilterListSection = <T extends Record<string, any>>({
  title,
  data,
  activeFilters = [],
  onSelectFilter,
  idKey,
  labelKey,
  numColumns = 2,
  scrollEnabled = false,
  isLoading = false,
  isAccordion = true,
  children,
  withSearchInput = false,
}: BaseFilterListSectionProps<T>) => {
  const [activeAccordion, setActiveAccordion] = useState<string[]>(['1'])
  const [searchInputValue, setSearchInputValue] = useState('')
  const { t } = useTranslation()

  // Filter data based on search input
  const filteredData = searchInputValue.trim()
    ? data.filter((item) =>
        String(item[labelKey]).toLowerCase().includes(searchInputValue.toLowerCase().trim()),
      )
    : data

  const renderItem = ({ item }: { item: T }) => {
    const itemId = String(item[idKey])
    const itemLabel = String(item[labelKey])
    const isSelected = activeFilters?.includes(itemId)

    return (
      <View className="flex-1">
        <TouchableOpacity
          activeOpacity={0.55}
          className={cn(
            'min-h-12 items-center justify-center rounded-lg border px-3 py-1',
            isSelected
              ? 'border-primary bg-primary-50'
              : 'border-transparent bg-custom-background-hover',
          )}
          onPress={() => onSelectFilter?.(item)}
        >
          <Text
            size="body7"
            variant={isSelected ? 'primary' : 'default'}
            className="text-center "
            numberOfLines={2}
          >
            {itemLabel}
          </Text>
        </TouchableOpacity>
      </View>
    )
  }

  const renderEmptyComponent = () => {
    return (
      <View className="flex items-center justify-center py-8">
        <Text size="body6" variant="subdued" className="text-center">
          {t('MES-812')}
        </Text>
      </View>
    )
  }

  return (
    <>
      {isAccordion ? (
        <Accordion
          variant="unfilled"
          defaultValue={['1']}
          className="w-full "
          type="single"
          onValueChange={(value) => {
            setActiveAccordion(value)
          }}
        >
          <AccordionItem value="1" className={cn('w-full  ')}>
            <AccordionTrigger className="w-full px-0">
              <View className="w-full flex-row items-center justify-between">
                <View className="flex-row items-center gap-x-2">
                  <Text size="body10">{title}</Text>
                </View>
                <View
                  className={cn(
                    'transition-all',
                    activeAccordion.includes('1') ? 'rotate-180' : '',
                  )}
                >
                  <ArrowDownIcon width={18} height={18} />
                </View>
              </View>
            </AccordionTrigger>
            <AccordionContent className="px-0">
              {isLoading ? (
                <FilterListSectionLoading />
              ) : (
                <View className="flex flex-col gap-y-4">
                  {withSearchInput && (
                    <SearchInput
                      placeholder={t('MES-66')}
                      value={searchInputValue}
                      onChangeText={(text) => setSearchInputValue(text)}
                      onClear={() => setSearchInputValue('')}
                    />
                  )}

                  <FlatList
                    data={filteredData}
                    keyExtractor={(item: T) => String(item[idKey])}
                    renderItem={renderItem}
                    numColumns={numColumns}
                    ListEmptyComponent={renderEmptyComponent}
                    contentContainerStyle={{ gap: 8 }}
                    scrollEnabled={false}
                    columnWrapperStyle={{
                      gap: 8,
                      justifyContent: 'flex-start',
                    }}
                  />
                  {children}
                </View>
              )}
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      ) : (
        <>
          <FlatList
            data={filteredData}
            keyExtractor={(item: T) => String(item[idKey])}
            renderItem={renderItem}
            numColumns={numColumns}
            contentContainerStyle={{ gap: 8 }}
            scrollEnabled={false}
            columnWrapperStyle={{
              gap: 8,
              justifyContent: 'flex-start',
            }}
          />
          {children}
        </>
      )}
    </>
  )
}

const FilterListSectionLoading = () => {
  return (
    <View className="flex w-full flex-1 flex-col gap-y-2">
      <View className="flex  flex-row gap-x-2">
        <Skeleton className="h-12 flex-1 rounded-lg" />
        <Skeleton className="h-12 flex-1 rounded-lg" />
      </View>
      <View className="flex  flex-row gap-x-2">
        <Skeleton className="h-12 flex-1 rounded-lg" />
        <Skeleton className="h-12 flex-1 rounded-lg" />
      </View>
    </View>
  )
}
