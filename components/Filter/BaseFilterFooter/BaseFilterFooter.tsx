import { forwardRef } from 'react'
import { View } from 'react-native'
import { BaseFilterBadgeList, BaseFilterItem } from '../BaseFilterBadgeList/BaseFilterBadgeList'
import { BaseFilterFooterActions } from '../BaseFilterFooterActions/BaseFilterFooterActions'

interface FilterBadgeProps<T extends BaseFilterItem> {
  filterItem: T
  onClearFilter?: (item: T) => void
}

interface BaseFilterFooterProps<T extends BaseFilterItem> {
  // Badge list props
  activeFilters: T[]
  onClearFilter: (filterItem: T) => void
  maxDisplayCount?: number
  BadgeComponent?: React.ComponentType<FilterBadgeProps<T>>
  selectedFiltersLabel?: string
  showMoreText?: string
  showLessText?: string

  // Footer actions props
  onApply: () => void
  onReset: () => void
  applyText?: string
  resetText?: string
  applyButtonProps?: {
    variant?: 'solid' | 'outline' | 'link'
    size?: 'sm' | 'md' | 'lg'
    className?: string
  }
  resetButtonProps?: {
    variant?: 'solid' | 'outline' | 'link'
    size?: 'sm' | 'md' | 'lg'
    className?: string
  }

  // Container styling
  containerClassName?: string
  actionsContainerClassName?: string
}

export const BaseFilterFooter = forwardRef<View, BaseFilterFooterProps<any>>(
  <T extends BaseFilterItem>(
    {
      // Badge list props
      activeFilters,
      onClearFilter,
      maxDisplayCount,
      BadgeComponent,
      selectedFiltersLabel,
      showMoreText,
      showLessText,

      // Footer actions props
      onApply,
      onReset,
      applyText,
      resetText,
      applyButtonProps,
      resetButtonProps,

      // Container styling
      containerClassName = 'flex relative flex-col gap-y-4 border-t border-gray-200 bg-white px-4 py-4 pb-10',
      actionsContainerClassName,
    }: BaseFilterFooterProps<T>,
    ref: React.ForwardedRef<View>,
  ) => {
    return (
      <View ref={ref} className={containerClassName}>
        {/* Selected Filter Badges */}
        <BaseFilterBadgeList
          activeFilters={activeFilters}
          onClearFilter={onClearFilter}
          maxDisplayCount={maxDisplayCount}
          BadgeComponent={BadgeComponent}
          selectedFiltersLabel={selectedFiltersLabel}
          showMoreText={showMoreText}
          showLessText={showLessText}
        />
        {/* Footer Actions */}
        <BaseFilterFooterActions
          onApply={onApply}
          onReset={onReset}
          applyText={applyText}
          resetText={resetText}
          applyButtonProps={applyButtonProps}
          resetButtonProps={resetButtonProps}
          containerClassName={actionsContainerClassName}
        />
      </View>
    )
  },
)

// Export types for easier usage
export type { BaseFilterItem, FilterBadgeProps }

// Type helper for creating typed versions of the component
export const createTypedFilterFooter = <T extends BaseFilterItem>() => {
  return BaseFilterFooter as React.ForwardRefExoticComponent<
    BaseFilterFooterProps<T> & React.RefAttributes<View>
  >
}
