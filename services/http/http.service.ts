import { SESSION_EXPIRED_EVENT_NAME } from '@/constants/event.constant'
import { ACCESS_TOKEN, PRIMARY_LANG, X_LOGIN_SESSION } from '@/constants/storage-key.constant'
import { HttpMethod } from '@/enums/http.enum'
import { Params } from '@/types/http.type'
import AsyncStorage from '@react-native-async-storage/async-storage'
import axios, {
  AxiosHeaders,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  isAxiosError,
} from 'axios'
import { stringify } from 'qs-esm'
import { DeviceEventEmitter } from 'react-native'
import { secureStoreService } from '../secure-store/secure-store.service'
class HttpService {
  private static instance: HttpService
  public http: AxiosInstance
  private baseURL = `${process.env.EXPO_PUBLIC_BASE_API_URL}/api`

  private constructor() {
    this.http = axios.create({
      baseURL: this.baseURL,
      withCredentials: true,
    })

    this.injectInterceptors()
  }

  public static getInstance(): HttpService {
    if (!HttpService.instance) {
      HttpService.instance = new HttpService()
    }
    return HttpService.instance
  }

  private async getAuthorizationHeaders(): Promise<Record<string, string>> {
    const token = await secureStoreService.getItem(ACCESS_TOKEN)
    const sessionId = await secureStoreService.getItem(X_LOGIN_SESSION)

    const headers: Record<string, string> = {}

    if (token) {
      headers['Authorization'] = `JWT ${token}`
    }

    if (sessionId) {
      headers[X_LOGIN_SESSION] = sessionId
    }

    return headers
  }

  private injectInterceptors() {
    this.http.interceptors.request.use(async (config) => {
      const authHeaders = await this.getAuthorizationHeaders()

      const mergedHeaders = new AxiosHeaders({
        ...authHeaders,
        ...(config.headers || {}),
      })
      const lang = await AsyncStorage.getItem(PRIMARY_LANG)
      if (lang) {
        mergedHeaders['accept-Language'] = lang
      }
      config.headers = mergedHeaders

      return config
    })

    this.http.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.data) {
          this.checkSessionInvalidation(error.response.data)
        }
        return Promise.reject(error)
      },
    )
  }

  // Helper method to check for session invalidation in error data
  private checkSessionInvalidation(errorData: any): void {
    // Check for sessionInvalid flag in the error data
    const sessionInvalid = errorData?.errors?.[0]?.data?.sessionInvalid

    if (sessionInvalid) {
      // Clear secure storage on session invalidation
      secureStoreService.removeItem(ACCESS_TOKEN)
      secureStoreService.removeItem(X_LOGIN_SESSION)

      //Emit event to handle session invalidation
      DeviceEventEmitter.emit(SESSION_EXPIRED_EVENT_NAME)
    }
  }

  private async request<T>(
    method: HttpMethod,
    url: string,
    options: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const response: AxiosResponse<T> = await this.http.request<T>({
        method,
        url,

        ...options,
      })
      return response.data
    } catch (error) {
      throw this.normalizeAxiosError(error)
    }
  }

  public async get<T>(url: string, options: AxiosRequestConfig = {}): Promise<T> {
    return this.request<T>(HttpMethod.GET, url, {
      ...options,
      paramsSerializer: (params) => {
        return stringify(params)
      },
    })
  }

  public async post<T>(url: string, data?: unknown, options: AxiosRequestConfig = {}): Promise<T> {
    return this.request<T>(HttpMethod.POST, url, { ...options, data })
  }

  public async put<T>(url: string, data?: unknown, options: AxiosRequestConfig = {}): Promise<T> {
    return this.request<T>(HttpMethod.PUT, url, { ...options, data })
  }

  public async patch<T>(url: string, data?: unknown, options: AxiosRequestConfig = {}): Promise<T> {
    return this.request<T>(HttpMethod.PATCH, url, { ...options, data })
  }

  public async delete<T>(url: string, options: AxiosRequestConfig = {}): Promise<T> {
    return this.request<T>(HttpMethod.DELETE, url, { ...options })
  }

  public async getWithMethodOverride<T>(
    url: string,
    params?: Params,
    options: AxiosRequestConfig = {},
  ): Promise<T> {
    const headers = {
      'Content-Type': 'application/x-www-form-urlencoded',
      'X-HTTP-Method-Override': 'GET',
      ...(options.headers || {}),
    }

    const formattedParams = params?.hasOwnProperty('params') ? { ...params?.params } : params

    return this.request<T>(HttpMethod.POST, url, {
      ...options,
      headers,
      data: stringify(formattedParams),
      withCredentials: true,
    })
  }

  private normalizeAxiosError(error: any) {
    if (isAxiosError(error)) {
      const responseData = error.response?.data ?? {}

      // Check for session invalidation in the normalized error
      this.checkSessionInvalidation(responseData)

      // Preserve the complete API error response structure when possible
      const normalizedError = {
        message:
          responseData?.errors?.[0]?.message || responseData?.message || error.message || 'MES-193',
        status: error.response?.status || 500,
        rawError: error,
        // Preserve API error structure
        apiError: responseData,
        // Extract multiple messages if available
        messages:
          responseData?.messages || responseData?.errors?.map((err: any) => err.message) || [],
        // Extract error code if available
        code: responseData?.code || responseData?.errors?.[0]?.code,
        // Preserve additional error data
        data: responseData?.errors?.[0]?.data || responseData?.data,
      }

      return normalizedError
    }

    return {
      message: 'MES-193',
      status: 500,
      rawError: error,
      apiError: null,
      messages: [],
      code: null,
      data: null,
    }
  }
}

export const httpService = HttpService.getInstance()
export { HttpService }
