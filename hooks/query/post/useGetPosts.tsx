import { postService } from '@/services/post/post.service'
import { PaginatedDocs } from '@/types/global.type'
import { Params } from '@/types/http.type'
import { Post } from '@/types/post.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { postQueryKeys } from './queryKeys'

export const useGetPosts = ({
  params = {},
  options = {},
  useQueryOptions,
  overrideKey,
}: {
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<PaginatedDocs<Post> | null>, 'queryKey' | 'queryFn'>
  overrideKey?: (string | Params)[]
} = {}) => {
  const {
    isError: isGetPostsError,
    isPending: isGetPostsLoading,
    data: posts,
    ...rest
  } = useQuery({
    queryKey: overrideKey ? overrideKey : [postQueryKeys['posts'].base(), params],
    queryFn: async () =>
      postService.getPosts({
        params: params,
        options: options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetPostsError,
    isGetPostsLoading,
    posts,
    ...rest,
  }
}
