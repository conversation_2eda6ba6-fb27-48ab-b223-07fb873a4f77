import type { ExpoConfig } from '@expo/config'
import 'dotenv/config'
import 'tsx/cjs'
import { getGoogleCredentials } from './configs/google-credentials/index'

const googleServicesPlist = getGoogleCredentials()?.googleServicesPlist
const googleServicesJson = getGoogleCredentials()?.googleServicesJson

const baseConfig: Partial<ExpoConfig> = {
  name: 'HICO',
  slug: 'hico',
  version: '2.2.3',
  orientation: 'portrait',
  icon: './assets/images/logo.png',
  scheme: 'com.wap.hico',
  userInterfaceStyle: 'automatic',
  newArchEnabled: true,
  splash: {
    image: './assets/images/logo.png',
    resizeMode: 'contain',
    backgroundColor: '#ffffff',
    imageWidth: 140,
  },

  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.wap.hico',
    googleServicesFile: googleServicesPlist,
    infoPlist: {
      NSCameraUsageDescription:
        'Allow HICO access to take photos for sending in chat and personalizing your avatar.',
      // NSLocationWhenInUseUsageDescription:
      //   'Allow this app to access your location while using the app',
      // NSContactsUsageDescription: 'Allow this app to access your contacts',
      ITSAppUsesNonExemptEncryption: false,
      UIBackgroundModes: ['audio', 'remote-notification'],
    },
    entitlements: {
      'aps-environment': 'production',
    },
    usesAppleSignIn: true,
    associatedDomains: [
      'applinks:japanmedicalgate.minastik.com',
      'applinks:japanmedicalgate.qa.minastik.com',
      'applinks:hico.co.jp',
    ],
  },

  android: {
    package: 'com.wap.hico.customer',
    googleServicesFile: googleServicesJson,
    edgeToEdgeEnabled: true,
    permissions: [
      'android.permission.READ_EXTERNAL_STORAGE',
      'android.permission.WRITE_EXTERNAL_STORAGE',
      'android.permission.MODIFY_AUDIO_SETTINGS',
      'android.permission.CAMERA',
    ],
    // adaptiveIcon: {
    //   foregroundImage: './assets/images/adaptive-icon.png',
    //   backgroundColor: '#ffffff',
    // },
    intentFilters: [
      {
        action: 'VIEW',
        autoVerify: true,
        data: [
          { scheme: 'com.wap.hico', host: 'oauthredirect' },

          { scheme: 'https', host: 'japanmedicalgate.minastik.com', pathPrefix: '/' },
          { scheme: 'https', host: 'japanmedicalgate.minastik.com', pathPrefix: '/products' },
          { scheme: 'https', host: 'japanmedicalgate.minastik.com', pathPrefix: '/products-v2' },
          { scheme: 'https', host: 'japanmedicalgate.minastik.com', pathPrefix: '/posts' },
          {
            scheme: 'https',
            host: 'japanmedicalgate.minastik.com',
            pathPrefix: '/medical-dictionary',
          },

          { scheme: 'https', host: 'japanmedicalgate.qa.minastik.com', pathPrefix: '/' },
          { scheme: 'https', host: 'japanmedicalgate.qa.minastik.com', pathPrefix: '/products' },
          { scheme: 'https', host: 'japanmedicalgate.qa.minastik.com', pathPrefix: '/products-v2' },
          { scheme: 'https', host: 'japanmedicalgate.qa.minastik.com', pathPrefix: '/posts' },
          {
            scheme: 'https',
            host: 'japanmedicalgate.qa.minastik.com',
            pathPrefix: '/medical-dictionary',
          },

          { scheme: 'https', host: 'hico.co.jp', pathPrefix: '/' },
          { scheme: 'https', host: 'hico.co.jp', pathPrefix: '/products' },
          { scheme: 'https', host: 'hico.co.jp', pathPrefix: '/products-v2' },
          { scheme: 'https', host: 'hico.co.jp', pathPrefix: '/posts' },
          { scheme: 'https', host: 'hico.co.jp', pathPrefix: '/medical-dictionary' },
        ],
        category: ['BROWSABLE', 'DEFAULT'],
      },
    ],
  },
  androidStatusBar: {
    barStyle: 'dark-content',
    backgroundColor: '#ffffff',
    translucent: false,
  },

  web: {
    bundler: 'metro',
    output: 'static',
    favicon: './assets/images/favicon.png',
  },

  experiments: {
    typedRoutes: true,
  },

  extra: {
    router: {},
    eas: {
      projectId: 'd1a2d577-c2ff-4b68-ba1b-f80df20e00e5',
    },
  },
  updates: {
    url: 'https://u.expo.dev/d1a2d577-c2ff-4b68-ba1b-f80df20e00e5',
  },
  runtimeVersion: '2.2.2',
  notification: {
    icon: './assets/images/logo-noti.png',
    // color: '#ffffff',
  },
}

const sharedPlugins: ExpoConfig['plugins'] = [
  'expo-router',

  [
    'expo-splash-screen',
    {
      image: './assets/images/logo.png',
      imageWidth: 140,
      resizeMode: 'contain',
      backgroundColor: '#ffffff',
    },
  ],
  [
    'expo-audio',
    // {
    //   microphonePermission: 'Allow to access your microphone.',
    // },
  ],
  [
    'expo-secure-store',
    {
      configureAndroidBackup: true,
      faceIDPermission:
        'Allow HICO to access Face ID to securely sign in and protect your account.',
    },
  ],
  [
    'expo-image-picker',
    {
      photosPermission:
        'Allow HICO access to your photos to send images in chat and update your avatar.',
    },
  ],

  [
    'expo-web-browser',
    {
      experimentalLauncherActivity: true,
    },
  ],
  [
    'expo-notifications',
    {
      icon: './assets/images/logo-noti.png',
      defaultChannel: 'default',
      enableBackgroundRemoteNotifications: true,

      // color: '#ffffff',
      // sounds: ['./momo_noti_sound.mp3'],
    },
  ],
  '@react-native-firebase/app',
  '@react-native-firebase/messaging',
  [
    'expo-build-properties',
    {
      ios: {
        useFrameworks: 'static',
      },
    },
  ],
  'expo-apple-authentication',
  './plugins/withAndroidManifestFix.ts',
]

const devConfig: Partial<ExpoConfig> = {
  ...baseConfig,
  plugins: sharedPlugins,
}

const prodConfig: Partial<ExpoConfig> = {
  ...baseConfig,
  plugins: [
    ...sharedPlugins,
    [
      '@react-native-google-signin/google-signin',
      {
        iosUrlScheme: process.env.EXPO_IOS_URL_SCHEME,
      },
    ],
  ],
}

export default ({ mode }: { expoConfig: ExpoConfig; mode: string }) => {
  return process.env.EXPO_PUBLIC_APP_ENVIROMENT === 'production' ? prodConfig : devConfig
}
